import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/shared/rest_api/api_request.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/notification_model.dart';

/// Repository for notification operations
final class NotificationRepository {
  /// Notification repository constructor
  const NotificationRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// Get notification list
  Future<ApiResult<List<NotificationModel>>> getNotificationList(
    ApiRequest request,
  ) {
    return DioRequest<List<NotificationModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<NotificationModel>(
            (e) => NotificationModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }
}
