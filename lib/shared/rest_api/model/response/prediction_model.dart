class PredictionModel {
  String? description;
  String? id;
  int? distanceMeters;
  String? placeId;
  String? reference;

  PredictionModel(
      {this.description,
      this.id,
      this.distanceMeters,
      this.placeId,
      this.reference});

  PredictionModel.fromJson(Map<String, dynamic> json) {
    description =
        json['description'] == null ? '' : json['description'].toString();
    id = json['id'] == null ? '' : json['id'].toString();
    distanceMeters = json['distance_meters'] == null
        ? 0
        : int.parse(json['distance_meters'].toString());
    placeId = json['place_id'] == null ? '' : json['place_id'].toString();
    reference = json['reference'] == null ? '' : json['reference'].toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['description'] = description;
    data['id'] = id;
    data['distance_meters'] = distanceMeters;
    data['place_id'] = placeId;
    data['reference'] = reference;
    return data;
  }
}
