import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/shared/rest_api/api_request.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/userinfo_model.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/zone_model.dart';

final class AuthRepository {
  /// Auth repository constructor
  const AuthRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// User registration
  Future<ApiResult<Map<String, dynamic>>> registration(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// Document verification
  Future<ApiResult<Map<String, dynamic>>> documentVerification(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// User login
  Future<ApiResult<UserInfoModel>> login(
    ApiRequest request,
  ) {
    return DioRequest<UserInfoModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: UserInfoModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// Social media login
  Future<ApiResult<UserInfoModel>> loginWithSocialMedia(
    ApiRequest request,
  ) {
    return DioRequest<UserInfoModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: UserInfoModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// Social media registration
  Future<ApiResult<UserInfoModel>> registerWithSocialMedia(
    ApiRequest request,
  ) {
    return DioRequest<UserInfoModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: UserInfoModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// Forget password
  Future<ApiResult<Map<String, dynamic>>> forgetPassword(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// Verify token
  Future<ApiResult<Map<String, dynamic>>> verifyToken(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// Reset password
  Future<ApiResult<Map<String, dynamic>>> resetPassword(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// Get zone list
  Future<ApiResult<List<ZoneModel>>> getZoneList(
    ApiRequest request,
  ) {
    return DioRequest<List<ZoneModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<ZoneModel>((e) => ZoneModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }
}
