import 'package:flutter/material.dart';
import 'package:staffmeal_user_v2/utils/dimensions.dart';

class PaginatedListView extends StatefulWidget {
  final ScrollController scrollController;
  final Function(int offset) onPaginate;
  final int totalSize;
  final int offset;
  final Widget productView;
  final bool enabledPagination;
  final bool reverse;
  const PaginatedListView({
    Key? key,
    required this.scrollController,
    required this.onPaginate,
    required this.totalSize,
    required this.offset,
    required this.productView,
    this.enabledPagination = true,
    this.reverse = false,
  }) : super(key: key);

  @override
  State<PaginatedListView> createState() => _PaginatedListViewState();
}

class _PaginatedListViewState extends State<PaginatedListView> {
  int? _offset;
  List<int>? _offsetList;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    _offset = 1;
    _offsetList = [1];

    widget.scrollController.addListener(() {
      if (widget.scrollController.position.pixels ==
              widget.scrollController.position.maxScrollExtent &&
          !_isLoading &&
          widget.enabledPagination) {
        if (mounted) {
          _paginate();
        }
      }
    });
  }

  void _paginate() async {
    int pageSize = (widget.totalSize / 10).ceil();
    if ((_offset ?? 0) < pageSize &&
        !(_offsetList ?? []).contains((_offset ?? 0) + 1)) {
      setState(() {
        _offset = (_offset ?? 0) + 1;
        _offsetList!.add((_offset ?? 0));
        _isLoading = true;
      });
      await widget.onPaginate((_offset ?? 0));
      setState(() {
        _isLoading = false;
      });
    } else {
      if (_isLoading) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    _offset = widget.offset;
    _offsetList = [];
    for (int index = 1; index <= widget.offset; index++) {
      _offsetList!.add(index);
    }

    return Column(
      children: [
        widget.reverse ? const SizedBox() : widget.productView,
        (
                ((_offset ?? 0) >= (widget.totalSize / 10).ceil() ||
                    _offsetList!.contains((_offset ?? 0) + 1)))
            ? const SizedBox()
            : Center(
                child: Padding(
                  padding: (_isLoading )
                      ? const EdgeInsets.all(Dimensions.PADDING_SIZE_SMALL)
                      : EdgeInsets.zero,
                  child: _isLoading
                      ? const CircularProgressIndicator()
                      : const SizedBox(),
                ),
              ),
        widget.reverse ? widget.productView : const SizedBox(),
      ],
    );
  }
}
