/// notification type enum extension
enum NotificationType {
  /// normal notification
  normal;

  /// from string
  static NotificationType fromString(String value) {
    switch (value) {
      case 'NORMAL':
        return NotificationType.normal;
      default:
        return NotificationType.normal;
    }
  }
}

/// notification type extension
extension NotificationTypeExtension on NotificationType {
  /// get name
  String get name {
    switch (this) {
      case NotificationType.normal:
        return 'NORMAL';
    }
  }
}
