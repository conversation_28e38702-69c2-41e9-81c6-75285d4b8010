import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:staffmeal_user_v2/res/theme_dark.dart';
import 'package:staffmeal_user_v2/res/theme_light.dart';
import 'package:staffmeal_user_v2/routes/app_router.dart';

/// [MyApp] is the root widget of the app.
class MyApp extends StatelessWidget {
  /// Constructor
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final data = MediaQuery.of(context);

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: ScreenUtilInit(
        designSize: const Size(430, 932), // Figma page size
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (suContext, child) => MediaQuery(
          data: data.copyWith(textScaler: TextScaler.noScaling),
          child: MaterialApp.router(
            debugShowCheckedModeBanner: false,
            theme: lightTheme,
            darkTheme: darkTheme,
            themeMode: ThemeMode.light,
            routerConfig: appRouter,
          ),
        ),
      ),
    );
  }
}
