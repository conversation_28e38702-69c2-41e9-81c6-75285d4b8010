import 'package:go_router/go_router.dart';

/// extension for [GoRouter]
extension GoRouterEx on GoRouter {
  /// Pop-until call
  /// path parameters key should be the same as from app router path
  void popUntil({required String pageName, Map<String, String>? pathParameters}) {
    final fullHistoryUris = routerDelegate.currentConfiguration.matches.map((e) => e.matchedLocation).toList();
    final popToPath = (pathParameters != null)
        ? namedLocation(
            pageName,
            pathParameters: pathParameters,
          )
        : namedLocation(pageName);
    // when popToPath page is same as the current open page then return
    if (fullHistoryUris.last == popToPath) {
      return;
    }

    // check page is already open and pop to it
    if (fullHistoryUris.contains(popToPath)) {
      do {
        if (canPop()) {
          pop();
          fullHistoryUris.removeLast();
        }
      } while (canPop() && popToPath != fullHistoryUris.last);
    }
  }
}
