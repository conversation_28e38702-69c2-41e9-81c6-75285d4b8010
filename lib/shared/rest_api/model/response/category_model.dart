class CategoryModel {
  int? _id;
  String? _name;
  int? _parentId;
  int? _position;
  int? _status;
  String? _createdAt;
  String? _updatedAt;
  String? _image;

  CategoryModel(
      {int? id,
      String? name,
      int? parentId,
      int? position,
      int? status,
      String? createdAt,
      String? updatedAt,
      String? image}) {
    _id = id;
    _name = name;
    _parentId = parentId;
    _position = position;
    _status = status;
    _createdAt = createdAt;
    _updatedAt = updatedAt;
    _image = image;
  }

  int? get id => _id;
  String? get name => _name;
  int? get parentId => _parentId;
  int? get position => _position;
  int? get status => _status;
  String? get createdAt => _createdAt;
  String? get updatedAt => _updatedAt;
  String? get image => _image;

  CategoryModel.fromJson(Map<String, dynamic> json) {
    _id = json['id'] == null ? 0 : int.parse(json['id'].toString());
    _name = json['name'] == null ? '' : json['name'].toString();
    _parentId =
        json['parent_id'] == null ? 0 : int.parse(json['parent_id'].toString());
    _position =
        json['position'] == null ? 0 : int.parse(json['position'].toString());
    _status = json['status'] == null ? 0 : int.parse(json['status'].toString());
    _createdAt =
        json['created_at'] == null ? '' : json['created_at'].toString();
    _updatedAt =
        json['updated_at'] == null ? '' : json['updated_at'].toString();
    _image = json['image'] == null ? '' : json['image'].toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = _id;
    data['name'] = _name;
    data['parent_id'] = _parentId;
    data['position'] = _position;
    data['status'] = _status;
    data['created_at'] = _createdAt;
    data['updated_at'] = _updatedAt;
    data['image'] = _image;
    return data;
  }
}
