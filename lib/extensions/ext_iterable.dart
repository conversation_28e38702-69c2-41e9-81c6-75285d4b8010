/// Iterable extensions.
extension IterableX<T> on Iterable<T> {
  /// add item if condition is true
  Iterable<T> addIf(bool Function() condition, T item) => condition() ? (toList()..add(item)) : this;

  /// group By key
  Map<K, List<T>> groupBy<K>(K Function(T) key) {
    final map = <K, List<T>>{};
    for (final element in this) {
      (map[key(element)] ??= []).add(element);
    }
    return map;
  }
}
