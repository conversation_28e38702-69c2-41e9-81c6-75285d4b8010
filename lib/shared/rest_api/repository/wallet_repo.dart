import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/shared/rest_api/api_request.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/wallet_model.dart';

final class WalletRepository {
  /// Wallet repository constructor
  const WalletRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// Get wallet transaction list
  Future<ApiResult<List<WalletModel>>> getWalletTransactionList(
    ApiRequest request,
  ) {
    return DioRequest<List<WalletModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<WalletModel>(
            (e) => WalletModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get loyalty transaction list
  Future<ApiResult<List<WalletModel>>> getLoyaltyTransactionList(
    ApiRequest request,
  ) {
    return DioRequest<List<WalletModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<WalletModel>(
            (e) => WalletModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Point to wallet
  Future<ApiResult<Map<String, dynamic>>> pointToWallet(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }
}
