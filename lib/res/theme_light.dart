import 'package:flutter/material.dart';
import 'package:staffmeal_user_v2/res/dimension.dart';
import 'package:staffmeal_user_v2/res/theme_colors.dart';
import 'package:staffmeal_user_v2/res/theme_text_colors.dart';

/// Application Theme
///
final _lightThemeData = ThemeData.light(useMaterial3: false);

const _themeColors = ThemeColors(
  primary: Color(0xFFEF7822),
  scaffold: Color(0xFFF3F3F3),
  appBar: Colors.white,
);

const _themeTextColors = ThemeTextColors(
  text: Color(0xFF202324),
  textField: Color(0xFF202324),
  textFieldHint: Color(0xFF9F9F9F),
  textFieldBorder: Color(0xFFD3D3D3),
  textButtonDisabled: Color(0xFFBABFC4),
  textDisabled: Color(0xFFBABFC4),
  textFieldErrorBorder: Color(0xFFE84D4F),
  whiteTextColor: Color(0xFFFFFFFF),
  blackTextColor: Color(0xFF202324),
  historyViewTextColor: Color(0x00ffffff),
  contactTitleColor: Color(0xFFFF8A85),
  darkMistSubTitle: Color(0xFF3E4C59),
  kPurpleLight: Color(0xFFDABFFF),
  neonBlueLinksColor: Color(0xFF4881F4),
  communitySkipText: Color(0xFF8C62FF),
  chatTimeText: Color(0xFF616E7C),
  goldTextColor: Color(0xFFEF7822),
);

final TextTheme _textTheme = _lightThemeData.textTheme.copyWith(
  titleLarge: TextStyle(
    fontFamily: 'Roboto',
    fontWeight: FontWeight.w700,
    color: _themeTextColors.text,
    fontSize: AppSize.sp16,
    inherit: false,
    height: 1.5,
  ),
  titleMedium: TextStyle(
    fontFamily: 'Roboto',
    fontWeight: FontWeight.w700,
    color: _themeTextColors.text,
    // inherit: false,
    height: 1,
    fontSize: AppSize.sp14,
    letterSpacing: 0,
  ),
  titleSmall: TextStyle(
    fontFamily: 'Roboto',
    fontWeight: FontWeight.w600,
    color: _themeTextColors.text,
    // inherit: false,
    height: 1,
    fontSize: AppSize.sp14,
    letterSpacing: 0,
  ),
  bodyLarge: TextStyle(
    fontFamily: 'Roboto',
    fontWeight: FontWeight.w500,
    color: _themeTextColors.text,
    // inherit: false,
    height: 1,
    fontSize: AppSize.sp14,
    letterSpacing: 0,
  ),
  bodyMedium: TextStyle(
    fontFamily: 'Roboto',
    fontWeight: FontWeight.w400,
    color: _themeTextColors.text,
    // inherit: false,
    height: 1,
    fontSize: AppSize.sp14,
    letterSpacing: 0,
  ),
  bodySmall: TextStyle(
    fontFamily: 'Roboto',
    fontWeight: FontWeight.w300,
    color: _themeTextColors.text,
    // inherit: false,
    height: 1,
    fontSize: AppSize.sp14,
    letterSpacing: 0,
  ),
);

/// Tab bar theme
final _tabBarTheme = TabBarThemeData(
  labelColor: _themeTextColors.blackTextColor,
  labelStyle: _textTheme.titleLarge,
  unselectedLabelStyle: _textTheme.titleLarge,
  // indicator: UnderlineTabIndicator(
  //   borderSide: BorderSide(color: _themeColors.black),
  //   insets: EdgeInsets.symmetric(vertical: AppSize.h10),
  // ),
  overlayColor: WidgetStateProperty.all<Color>(Colors.grey.shade100),
  unselectedLabelColor: _themeTextColors.textDisabled,
);

/// Application Light Theme
final ThemeData lightTheme = _lightThemeData.copyWith(
  primaryColor: const Color(0xFFEF7822),
  secondaryHeaderColor: const Color(0xFF1ED7AA),
  disabledColor: const Color(0xFFBABFC4),
  hintColor: const Color(0xFF9F9F9F),
  cardColor: Colors.white,
  colorScheme: const ColorScheme.light(
    primary: Color(0xFFEF7822),
    secondary: Color(0xFFEF7822),
    surface: Color(0xFFF3F3F3),
    error: Color(0xFFE84D4F),
  ),
  splashColor: Colors.transparent,
  scaffoldBackgroundColor: _themeColors.scaffold,
  textTheme: _textTheme,
  appBarTheme: AppBarTheme(
    backgroundColor: _themeColors.appBar,
    centerTitle: true,
    scrolledUnderElevation: 0,
    elevation: 0,
    titleTextStyle: TextStyle(
      // fontFamily: FontFamily.grifter,
      fontWeight: FontWeight.w700,
      color: _themeTextColors.text,
      fontSize: AppSize.sp16,
      height: 1.5,
    ),
  ),
  filledButtonTheme: FilledButtonThemeData(
    style: FilledButton.styleFrom(
      foregroundColor: _themeTextColors.text,
      // disabledBackgroundColor: _themeColors.buttonDisabled,
      disabledForegroundColor: _themeTextColors.textButtonDisabled,
      maximumSize: Size(double.infinity, AppSize.h48),
      minimumSize: Size(AppSize.w64, AppSize.h48),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSize.r16),
      ),
      textStyle: _textTheme.bodyMedium?.copyWith(
        fontSize: AppSize.sp16,
        // fontFamily: FontFamily.grifter,
        fontWeight: FontWeight.w700,
        color: _themeTextColors.text,
        height: 1.5,
      ),
    ),
  ),
  outlinedButtonTheme: OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      foregroundColor: _themeColors.primary,
      // disabledBackgroundColor: _themeColors.buttonDisabled,
      disabledForegroundColor: _themeTextColors.textButtonDisabled,
      maximumSize: Size(double.infinity, AppSize.h48),
      minimumSize: Size(AppSize.w64, AppSize.h48),
      // side: BorderSide(color: _themeColors.buttonSecondaryColor),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSize.r14),
      ),
      textStyle: _textTheme.bodyMedium?.copyWith(
        fontSize: AppSize.sp16,
        //  fontFamily: FontFamily.grifter,
        fontWeight: FontWeight.w700,
        color: _themeTextColors.text,
        height: 1.5,
      ),
    ),
  ),
  textButtonTheme: TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: const Color(0xFFEF7822),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSize.r14),
      ),
      textStyle: _textTheme.bodyMedium?.copyWith(
        fontSize: AppSize.sp16,
        fontFamily: 'Roboto',
        fontWeight: FontWeight.w700,
        color: const Color(0xFFEF7822),
        height: 1.5,
      ),
    ),
  ),
  extensions: <ThemeExtension<dynamic>>[_themeColors, _themeTextColors],
  tabBarTheme: _tabBarTheme,
);
