import 'dart:convert';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:staffmeal_user_v2/extensions/ext_notification_type.dart';
import 'package:staffmeal_user_v2/services/notification_service/local_notifications_helper.dart';
import 'package:staffmeal_user_v2/utils/logger.dart';

// /// Global key for get the context
// GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

/// This is a Top-level function where it is used for handling background or
/// Terminated state notifications. This is optional if you don't use onBackgroundMessage stream
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  'Handling a background message: ${message.messageId}'.logD;
}

/// This class is helper for initialize Notification with get permission of user,
/// To handle foreground/background/terminated state notification.
class NotificationHelper {
  /// if notification is initialized or not
  static bool initialized = false;

  /// initialize and setup the notification for device.
  static Future<void> initializeNotification() async {
    if (initialized) return;
    initialized = true;
    // Getting the instance of firebase messaging
    final messaging = FirebaseMessaging.instance;
    await messaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
    // initialize local notification for foreground notification.
    await LocalNotificationHelper.localNotificationHelper.initialize();

    /// Request the notification permission to user,
    /// in Android 12 or below,by default Notification permission is granted.
    if (Platform.isIOS) {
      await messaging.requestPermission();
    }

    /// To send notification to specific device, we need specific device token.
    /// To get device token, we can use following method
    'device token ===>> ${await messaging.getToken()}'.logV;

    // we can also send notification using subscribe the topic.
    // await FirebaseMessaging.instance.subscribeToTopic("myTopic");
    /// To handle Foreground notification
    FirebaseMessaging.onMessage.listen((event) {
      // notificationOnTapHandler(remoteMessage: event);
      '1 notification =====>>> fg:  ${event.data.entries}'.logD;
      if (event.notification != null) {
        '2 notification =====>>> fg:  ${event.notification?.toMap().entries}'
            .logD;
        if (Platform.isAndroid) {
          '3 notification =====>>> fg:  {event.data.entries}'.logD;
          LocalNotificationHelper.localNotificationHelper.showNotification(
            event,
          );
        }
        final data = jsonDecode(event.data['message_data'].toString()) as Map;
        'message data     ===   $data'.logD;
        if (data.toString().contains('type')) {
          final type = data['type'];
          if (type.toString() == 'MESSAGE' ||
              type.toString() == 'REQUEST_TO_GO_LIVE') {
            'type: $type'.logD;

            // rootNavKey.currentContext?.read<ChatListBloc>().add(
            //       UpdateChatList(
            //         receiverConnectionId: int.parse(
            //           data['sender_connection_id'].toString(),
            //         ),
            //         senderConnectionId: int.parse(
            //           event.data['receiver_connection_id'].toString(),
            //         ),
            //         // receiverConnectionId: int.parse(
            //         //   event.data['receiver_connection_id'].toString(),
            //         // ),
            //         // senderConnectionId: int.parse(
            //         //   data['sender_connection_id'].toString(),
            //         // ),
            //         text: data['text'].toString(),
            //       ),
            //     );
          }
        }
      }
    });

    /// To handle Background/terminated app notification (This is optional.)
    // FirebaseMessaging.onBackgroundMessage(
    //   _firebaseMessagingBackgroundHandler,
    // );
    // To handle the Notification Tap Event on Background.
    FirebaseMessaging.onMessageOpenedApp.listen((event) {
      'notification =====>>> bg: $event'.logD;
      notificationOnTapHandler(remoteMessage: event);
      // final data = jsonDecode(
      //   event.data['data'].toString(),
      // );
      // if (data.toString().contains('type')) {
      //   if (data['type'].toString() == 'chat_message') {}
      // }
    });
    // To handle the Notification Tap Event on Terminated state.
    await FirebaseMessaging.instance.getInitialMessage().then((event) {
      'notification =====>>> tg:  $event'.logD;
      notificationOnTapHandler(remoteMessage: event);
    });
    // } else {
    //   // here you can give a message to the user if user not granted the permission.
    //   log('User declined the permission');
    // }
  }

  /// handle notification on tap
  static Future<void> notificationOnTapHandler({
    RemoteMessage? remoteMessage,
    NotificationResponse? localData,
    bool isLocal = false,
  }) async {
    final data = isLocal
        ? jsonDecode(localData?.payload ?? '{}') as Map<String, dynamic>
        : remoteMessage!.data;
    final type = NotificationType.fromString(data['type'].toString());
    data.logD;

    switch (type) {
      case NotificationType.normal:

        /// Normal Notification, open the app no need to navigate to any screen
        break;
    }
  }
}
