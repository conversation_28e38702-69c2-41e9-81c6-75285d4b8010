import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/product_model.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/restaurant_model.dart';
import 'package:staffmeal_user_v2/utils/dimensions.dart';

class ProductView extends StatefulWidget {
  final List<Product>? products;
  final List<Restaurant>? restaurants;
  final bool isRestaurant;
  final EdgeInsetsGeometry padding;
  final bool isScrollable;
  final int shimmerLength;
  final String? noDataText;
  final bool isCampaign;
  final bool inRestaurantPage;
  final bool? isData;
  final String? type;
  final Function(String type)? onVegFilterTap;
  final bool showTheme1Restaurant;
  ProductView({
    required this.restaurants,
    required this.products,
    required this.isRestaurant,
    this.isData = false,
    this.isScrollable = false,
    this.shimmerLength = 20,
    this.padding = const EdgeInsets.all(Dimensions.PADDING_SIZE_SMALL),
    this.noDataText,
    this.isCampaign = false,
    this.inRestaurantPage = false,
    this.type,
    this.onVegFilterTap,
    this.showTheme1Restaurant = false,
  });

  @override
  State<ProductView> createState() => _ProductViewState();
}

class _ProductViewState extends State<ProductView> {
  bool _isNull = true;
  int _length = 0;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    log('helllllllll==${widget.restaurants}');
    log('helllllllll==${widget.products}');
    log('helllllllll==${widget.isData}');
    if (widget.isRestaurant) {
      _isNull = widget.restaurants == null;
      if (!_isNull) {
        _length = widget.restaurants!.length;
      }
    } else {
      _isNull = widget.products == null;
      if (!_isNull) {
        _length = widget.products!.length;
      }
    }
    log('_length $_length');
    return Column(
      children: [
        widget.type != null
            ? VegFilterWidget(
                type: widget.type!,
                onSelected: widget.onVegFilterTap != null
                    ? widget.onVegFilterTap!
                    : (String) {},
              )
            : SizedBox(),
        !_isNull
            ? widget.isData == true
                  ? NoDataScreen(
                      text: widget.noDataText != null
                          ? widget.noDataText!
                          : widget.isRestaurant
                          ? 'no_restaurant_available'.tr
                          : 'no_food_available'.tr,
                    )
                  : _length > 0
                  ? GridView.builder(
                      key: UniqueKey(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisSpacing: Dimensions.PADDING_SIZE_LARGE,
                        mainAxisSpacing: ResponsiveHelper.isDesktop(context)
                            ? Dimensions.PADDING_SIZE_LARGE
                            : 0.01,
                        childAspectRatio: ResponsiveHelper.isDesktop(context)
                            ? 4
                            : widget.showTheme1Restaurant
                            ? 1.9
                            : 4,
                        crossAxisCount: ResponsiveHelper.isMobile(context)
                            ? 1
                            : 2,
                      ),
                      physics: widget.isScrollable
                          ? BouncingScrollPhysics()
                          : NeverScrollableScrollPhysics(),
                      shrinkWrap: widget.isScrollable ? false : true,
                      itemCount: _length,
                      padding: widget.padding,
                      itemBuilder: (context, index) {
                        log('data ==== $_length');
                        return widget.showTheme1Restaurant
                            ? RestaurantWidget(
                                restaurant: widget.restaurants?[index],
                                index: index,
                                inStore: widget.inRestaurantPage,
                              )
                            : ProductWidget(
                                isRestaurant: widget.isRestaurant,
                                product: widget.isRestaurant
                                    ? null
                                    : widget.products?[index],
                                restaurant: widget.isRestaurant
                                    ? (widget.restaurants?[index])
                                    : null,
                                index: index,
                                length: _length,
                                isCampaign: widget.isCampaign,
                                inRestaurant: widget.inRestaurantPage,
                              );
                      },
                    )
                  // : NoDataScreen(
                  //     text: noDataText != null
                  //         ? noDataText!
                  //         : isRestaurant
                  //             ? 'no_restaurant_available'.tr
                  //             : 'no_food_available'.tr,
                  //   )
                  : GridView.builder(
                      key: UniqueKey(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisSpacing: Dimensions.PADDING_SIZE_LARGE,
                        mainAxisSpacing: ResponsiveHelper.isDesktop(context)
                            ? Dimensions.PADDING_SIZE_LARGE
                            : 0.01,
                        childAspectRatio: ResponsiveHelper.isDesktop(context)
                            ? 4
                            : widget.showTheme1Restaurant
                            ? 1.9
                            : 4,
                        crossAxisCount: ResponsiveHelper.isMobile(context)
                            ? 1
                            : 2,
                      ),
                      physics: widget.isScrollable
                          ? BouncingScrollPhysics()
                          : NeverScrollableScrollPhysics(),
                      shrinkWrap: widget.isScrollable ? false : true,
                      itemCount: widget.shimmerLength,
                      padding: widget.padding,
                      itemBuilder: (context, index) {
                        log('_length asa $_length');
                        return widget.showTheme1Restaurant
                            ? RestaurantShimmer(isEnable: _isNull)
                            : ProductShimmer(
                                isEnabled: _isNull,
                                isRestaurant: widget.isRestaurant,
                                hasDivider: index != widget.shimmerLength - 1,
                              );
                      },
                    )
            : GridView.builder(
                key: UniqueKey(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisSpacing: Dimensions.PADDING_SIZE_LARGE,
                  mainAxisSpacing: ResponsiveHelper.isDesktop(context)
                      ? Dimensions.PADDING_SIZE_LARGE
                      : 0.01,
                  childAspectRatio: ResponsiveHelper.isDesktop(context)
                      ? 4
                      : widget.showTheme1Restaurant
                      ? 1.9
                      : 4,
                  crossAxisCount: ResponsiveHelper.isMobile(context) ? 1 : 2,
                ),
                physics: widget.isScrollable
                    ? BouncingScrollPhysics()
                    : NeverScrollableScrollPhysics(),
                shrinkWrap: widget.isScrollable ? false : true,
                itemCount: widget.shimmerLength,
                padding: widget.padding,
                itemBuilder: (context, index) {
                  return widget.showTheme1Restaurant
                      ? RestaurantShimmer(isEnable: _isNull)
                      : ProductShimmer(
                          isEnabled: _isNull,
                          isRestaurant: widget.isRestaurant,
                          hasDivider: index != widget.shimmerLength - 1,
                        );
                },
              ),
      ],
    );
  }

  // @override
  // Widget build(BuildContext context) {
  //   // Check if data is available
  //   if (widget.isRestaurant) {
  //     _isNull = widget.restaurants == null || widget.restaurants!.isEmpty;
  //     _length = widget.isNull ? 0 : widget.restaurants!.length;
  //   } else {
  //     _isNull = widget.products == null || widget.products!.isEmpty;
  //     _length = widget.isNull ? 0 : widget.products!.length;
  //   }

  //   log('_length $_length');

  //   return Column(children: [
  //     widget.type != null
  //         ? VegFilterWidget(type: widget.type!, onSelected: widget.onVegFilterTap ?? (String type) {})
  //         : SizedBox(),
  //     !_isNull
  //         //?
  //         //_length > 0
  //         ? GridView.builder(
  //             key: UniqueKey(),
  //             gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
  //               crossAxisSpacing: Dimensions.PADDING_SIZE_LARGE,
  //               mainAxisSpacing: ResponsiveHelper.isDesktop(context) ? Dimensions.PADDING_SIZE_LARGE : 0.01,
  //               childAspectRatio: ResponsiveHelper.isDesktop(context)
  //                   ? 4
  //                   : widget.showTheme1Restaurant
  //                       ? 1.9
  //                       : 4,
  //               crossAxisCount: ResponsiveHelper.isMobile(context) ? 1 : 2,
  //             ),
  //             physics: widget.isScrollable ? BouncingScrollPhysics() : NeverScrollableScrollPhysics(),
  //             shrinkWrap: widget.isScrollable ? false : true,
  //             itemCount: _length,
  //             padding: widget.padding,
  //             itemBuilder: (context, index) {
  //               log('_length of gridview $_length');
  //               return widget.showTheme1Restaurant
  //                   ? RestaurantWidget(
  //                       restaurant: widget.restaurants?[index], index: index, inStore: widget.inRestaurantPage)
  //                   : ProductWidget(
  //                       isRestaurant: widget.isRestaurant,
  //                       product: widget.isRestaurant ? null : widget.products?[index],
  //                       restaurant: widget.isRestaurant ? (widget.restaurants?[index]) : null,
  //                       index: index,
  //                       length: _length,
  //                       isCampaign: widget.isCampaign,
  //                       inRestaurant: widget.inRestaurantPage,
  //                     );
  //             },
  //           )
  //         // : NoDataScreen(
  //         //     text: widget.noDataText ?? (widget.isRestaurant
  //         //         ? 'no_restaurant_available'.tr
  //         //         : 'no_food_available'.tr),
  //         //   )
  //         : GridView.builder(
  //             key: UniqueKey(),
  //             gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
  //               crossAxisSpacing: Dimensions.PADDING_SIZE_LARGE,
  //               mainAxisSpacing: ResponsiveHelper.isDesktop(context) ? Dimensions.PADDING_SIZE_LARGE : 0.01,
  //               childAspectRatio: ResponsiveHelper.isDesktop(context)
  //                   ? 4
  //                   : widget.showTheme1Restaurant
  //                       ? 1.9
  //                       : 4,
  //               crossAxisCount: ResponsiveHelper.isMobile(context) ? 1 : 2,
  //             ),
  //             physics: widget.isScrollable ? BouncingScrollPhysics() : NeverScrollableScrollPhysics(),
  //             shrinkWrap: widget.isScrollable ? false : true,
  //             itemCount: widget.shimmerLength,
  //             padding: widget.padding,
  //             itemBuilder: (context, index) {
  //               return widget.showTheme1Restaurant
  //                   ? RestaurantShimmer(isEnable: _isNull)
  //                   : ProductShimmer(
  //                       isEnabled: _isNull,
  //                       isRestaurant: widget.isRestaurant,
  //                       hasDivider: index != widget.shimmerLength - 1);
  //             },
  //           ),
  //   ]);
  // }
}
