import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/shared/rest_api/api_request.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/onboarding_model.dart';

/// Repository for onboarding operations
final class OnboardingRepository {
  /// Onboarding repository constructor
  const OnboardingRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// Get onboarding list from server
  Future<ApiResult<Map<String, dynamic>>> getOnBoardingList(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get local onboarding data
  List<OnBoardingModel> getLocalOnBoardingList() {
    return [
      OnBoardingModel(
        'assets/images/onboard_1.png',
        'Welcome to StaffMeal',
        'Order delicious food from your favorite restaurants',
      ),
      OnBoardingModel(
        'assets/images/onboard_2.png',
        'Fast Delivery',
        'Get your food delivered quickly to your doorstep',
      ),
      OnBoardingModel(
        'assets/images/onboard_3.png',
        'Easy Payment',
        'Pay easily with multiple payment options',
      ),
    ];
  }
}
