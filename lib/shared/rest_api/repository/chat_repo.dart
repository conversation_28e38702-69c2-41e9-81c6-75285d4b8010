import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/shared/rest_api/api_request.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/conversation_model.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/message_model.dart';

/// Repository for chat operations
final class ChatRepository {
  /// Chat repository constructor
  const ChatRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// Get conversation list
  Future<ApiResult<List<Conversation>>> getConversationList(
    ApiRequest request,
  ) {
    return DioRequest<List<Conversation>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<Conversation>(
            (e) => Conversation.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Search conversation list
  Future<ApiResult<List<Conversation>>> searchConversationList(
    ApiRequest request,
  ) {
    return DioRequest<List<Conversation>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<Conversation>(
            (e) => Conversation.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get messages
  Future<ApiResult<List<Message>>> getMessages(
    ApiRequest request,
  ) {
    return DioRequest<List<Message>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<Message>(
            (e) => Message.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Send message
  Future<ApiResult<Map<String, dynamic>>> sendMessage(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }
}
