import 'package:staffmeal_user_v2/shared/rest_api/model/response/product_model.dart';

class OrderDetailsModel {
  int? id;
  int? foodId;
  String? orderId;
  double? price;
  Product? foodDetails;
  List<Variation>? variation;
  List<AddOns>? addOns;
  double? discountOnFood;
  String? discountType;
  int? quantity;
  double? taxAmount;
  String? variant;
  String? createdAt;
  String? updatedAt;
  int? itemCampaignId;
  double? totalAddOnPrice;

  OrderDetailsModel({
    this.id,
    this.foodId,
    this.orderId,
    this.price,
    this.foodDetails,
    this.variation,
    this.addOns,
    this.discountOnFood,
    this.discountType,
    this.quantity,
    this.taxAmount,
    this.variant,
    this.createdAt,
    this.updatedAt,
    this.itemCampaignId,
    this.totalAddOnPrice,
  });

  OrderDetailsModel.fromJson(Map<String, dynamic> json) {
    id = json['id'] == null ? 0 : int.parse(json['id'].toString());
    foodId = json['food_id'] == null
        ? 0
        : int.parse(json['food_id'].toString());
    orderId = json['order_id'] == null ? '' : json['order_id'].toString();
    price = json['price'] == null ? 0 : double.parse(json['price'].toString());
    foodDetails = json['food_details'] != null
        ? Product.fromJson(json['food_details'])
        : null;
    if (json['variation'] != null) {
      variation = [];
      json['variation'].forEach((dynamic v) {
        variation?.add(Variation.fromJson(v as Map<String, dynamic>));
      });
    }
    if (json['add_ons'] != null) {
      addOns = [];
      json['add_ons'].forEach((dynamic v) {
        addOns?.add(AddOns.fromJson(v as Map<String, dynamic>));
      });
    }
    discountOnFood = json['discount_on_food'] == null
        ? 0
        : double.parse(json['discount_on_food'].toString());
    discountType = json['discount_type'] == null
        ? ''
        : json['discount_type'].toString();
    quantity = json['quantity'] == null
        ? 0
        : int.parse(json['quantity'].toString());
    taxAmount = json['tax_amount'] == null
        ? 0
        : double.parse(json['tax_amount'].toString());
    variant = json['variant'] == null ? '' : json['variant'].toString();
    createdAt = json['created_at'] == null ? '' : json['created_at'].toString();
    updatedAt = json['updated_at'] == null ? '' : json['updated_at'].toString();
    itemCampaignId = json['item_campaign_id'] == null
        ? 0
        : int.parse(json['item_campaign_id'].toString());
    totalAddOnPrice = json['total_add_on_price'] == null
        ? 0
        : double.parse(json['total_add_on_price'].toString());
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['food_id'] = foodId;
    data['order_id'] = orderId;
    data['price'] = price;
    if (foodDetails != null) {
      data['food_details'] = foodDetails?.toJson();
    }
    if (variation != null) {
      data['variation'] = variation?.map((v) => v.toJson()).toList();
    }
    if (addOns != null) {
      data['add_ons'] = addOns?.map((v) => v.toJson()).toList();
    }
    data['discount_on_food'] = discountOnFood;
    data['discount_type'] = discountType;
    data['quantity'] = quantity;
    data['tax_amount'] = taxAmount;
    data['variant'] = variant;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['item_campaign_id'] = itemCampaignId;
    data['total_add_on_price'] = totalAddOnPrice;
    return data;
  }
}

/*class AddOns {
  String id;
  String name;
  List<Addons> addons;
  String required;
  String maxQuantity;
  String minQuantity;

  AddOns(
      {this.id,
        this.name,
        this.addons,
        this.required,
        this.maxQuantity,
        this.minQuantity
      });

  AddOns.fromJson(Map<String, dynamic> json) {
    id = json['id'].toString();
    name = json['name'].toString();
    if (json['addons'] != null) {
      addons = <Addons>[];
      json['addons'].forEach((dynamic v) {
        addons.add(Addons.fromJson(v as Map<String, dynamic>));
      });
    }
    required = json['required'].toString();
    maxQuantity = json['max_quantity'].toString();
    minQuantity = json['min_quantity'].toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    if (this.addons != null) {
      data['addons'] = this.addons.map((v) => v.toJson()).toList();
    }
    data['required'] = this.required;
    data['max_quantity'] = this.maxQuantity;
    data['min_quantity'] = this.minQuantity;
    return data;
  }
}

class Addons {
  String id;
  String name;
  String price;
  String quantity;
  List<AddonVariations> addonVariations;

  Addons({this.id, this.name, this.price, this.quantity, this.addonVariations});

  Addons.fromJson(Map<String, dynamic> json) {
    id = json['id'].toString();
    name = json['name'].toString();
    price = json['price'].toString();
    quantity = json['quantity'] != null ? json['quantity'].toString() : '0';
    if (json['variations'] != null) {
      addonVariations = <AddonVariations>[];
      json['variations'].forEach((dynamic v) {
        addonVariations.add(AddonVariations.fromJson(v as Map<String, dynamic>));
      });
    } else {
      addonVariations = [];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['price'] = this.price;
    data['quantity'] = this.quantity;
    if (this.addonVariations != null) {
      data['variations'] = this.addonVariations.map((v) => v.toJson()).toList();
    }
    return data;
  }
}*/
//
// class AddOn {
//   String name;
//   double price;
//   int quantity;
//
//   AddOn({this.name, this.price, this.quantity});
//
//   AddOn.fromJson(Map<String, dynamic> json) {
//     name = json['name'];
//     price = json['price'].toDouble();
//     quantity = json['quantity'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['name'] = this.name;
//     data['price'] = this.price;
//     data['quantity'] = this.quantity;
//     return data;
//   }
// }
