import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:staffmeal_user_v2/services/notification_service/notification_helper.dart';

/// local notification helper
class LocalNotificationHelper {
  LocalNotificationHelper._();

  /// local notification helper instance
  static final localNotificationHelper = LocalNotificationHelper._();

  /// flutter local notifications plugin
  static final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  /// android notification channel
  static const AndroidNotificationChannel channel = AndroidNotificationChannel(
    'default_channel',
    'Default Channel',
    importance: Importance.high,
  );

  /// android initialization settings
  static const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings('@mipmap/ic_launcher');

  /// darwin initialization settings
  static const DarwinInitializationSettings initializationSettingsDarwin =
      DarwinInitializationSettings();

  /// android notification details
  static AndroidNotificationDetails androidNotificationDetails =
      AndroidNotificationDetails(
        channel.id,
        channel.name,
        visibility: NotificationVisibility.public,
        importance: Importance.high,
        enableLights: true,
      );

  /// darwin notification details
  static const DarwinNotificationDetails darwinNotificationDetails =
      DarwinNotificationDetails(presentSound: true);

  /// initialize local notifications
  Future<void> initialize() async {
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.requestNotificationsPermission();

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
          IOSFlutterLocalNotificationsPlugin
        >()
        ?.requestPermissions(alert: true, badge: true, sound: true);

    await flutterLocalNotificationsPlugin.initialize(
      const InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsDarwin,
      ),
      onDidReceiveNotificationResponse: (details) {
        if (details.payload != '') {
          NotificationHelper.notificationOnTapHandler(
            localData: details,
            isLocal: true,
          );
        }
      },
    );

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(channel);
  }

  /// show notification
  Future<void> showNotification(RemoteMessage remoteMessage) async {
    await flutterLocalNotificationsPlugin.show(
      remoteMessage.notification.hashCode,
      remoteMessage.notification?.title,
      remoteMessage.notification?.body,
      NotificationDetails(
        android: androidNotificationDetails,
        iOS: darwinNotificationDetails,
      ),
      payload: remoteMessage.data.isNotEmpty
          ? jsonEncode(remoteMessage.data)
          : null,
    );
  }
}
