class CouponModel {
  int? id;
  String? title;
  String? code;
  String? startDate;
  String? expireDate;
  double? minPurchase;
  double? maxDiscount;
  double? discount;
  String? discountType;
  String? couponType;
  int? limit;
  String? data;
  String? createdAt;
  String? updatedAt;

  CouponModel(
      {this.id,
      this.title,
      this.code,
      this.startDate,
      this.expireDate,
      this.minPurchase,
      this.maxDiscount,
      this.discount,
      this.discountType,
      this.couponType,
      this.limit,
      this.data,
      this.createdAt,
      this.updatedAt});

  CouponModel.fromJson(Map<String, dynamic> json) {
    id = json['id'] == null ? 0 : int.parse(json['id'].toString());
    title = json['title'] == null ? '' : json['title'].toString();
    code = json['code'] == null ? '' : json['code'].toString();
    startDate = json['start_date'] == null ? '' : json['start_date'].toString();
    expireDate =
        json['expire_date'] == null ? '' : json['expire_date'].toString();
    minPurchase = json['min_purchase'] == null
        ? 0
        : double.parse(json['min_purchase'].toString());
    maxDiscount = json['max_discount'] == null
        ? 0
        : double.parse(json['max_discount'].toString());
    discount = json['discount'] == null
        ? 0
        : double.parse(json['discount'].toString());
    discountType =
        json['discount_type'] == null ? '' : json['discount_type'].toString();
    couponType =
        json['coupon_type'] == null ? '' : json['coupon_type'].toString();
    limit = json['limit'] == null ? 0 : int.parse(json['limit'].toString());
    data = json['data'] == null ? '' : json['data'].toString();
    createdAt = json['created_at'] == null ? '' : json['created_at'].toString();
    updatedAt = json['updated_at'] == null ? '' : json['updated_at'].toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['code'] = code;
    data['start_date'] = startDate;
    data['expire_date'] = expireDate;
    data['min_purchase'] = minPurchase;
    data['max_discount'] = maxDiscount;
    data['discount'] = discount;
    data['discount_type'] = discountType;
    data['coupon_type'] = couponType;
    data['limit'] = limit;
    data['data'] = this.data;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}
