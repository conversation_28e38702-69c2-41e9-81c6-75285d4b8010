enum NotificationType { message, order, general, block, verification }

class NotificationBody {
  NotificationType? notificationType;
  String? orderId;
  int? adminId;
  int? deliverymanId;
  int? restaurantId;
  String? type;
  String? senderType;
  String? message;
  int? conversationId;

  NotificationBody({
    this.notificationType,
    this.orderId,
    this.adminId,
    this.deliverymanId,
    this.restaurantId,
    this.type,
    this.senderType,
    this.message,
    this.conversationId,
  });

  NotificationBody.fromJson(Map<String, dynamic> json) {
    notificationType = convertToEnum(
      json['order_notification']?.toString() ?? '',
    );
    orderId = json['order_id']?.toString();
    adminId = json['admin_id'] != null
        ? int.tryParse(json['admin_id'].toString())
        : null;
    deliverymanId = json['deliveryman_id'] != null
        ? int.tryParse(json['deliveryman_id'].toString())
        : null;
    restaurantId = json['restaurant_id'] != null
        ? int.tryParse(json['restaurant_id'].toString())
        : null;
    type = json['type']?.toString();
    senderType = json['sender_type']?.toString();
    conversationId = json['conversation_id'] != null
        ? int.tryParse(json['conversation_id'].toString())
        : null;
    message = json['message']?.toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['order_notification'] = notificationType.toString();
    data['order_id'] = orderId;
    data['admin_id'] = adminId;
    data['deliveryman_id'] = deliverymanId;
    data['restaurant_id'] = restaurantId;
    data['type'] = type;
    data['sender_type'] = senderType;
    data['conversation_id'] = conversationId;
    data['message'] = message;
    return data;
  }

  NotificationType convertToEnum(String enumString) {
    if (enumString == NotificationType.general.toString()) {
      return NotificationType.general;
    } else if (enumString == NotificationType.order.toString()) {
      return NotificationType.order;
    } else if (enumString == NotificationType.message.toString()) {
      return NotificationType.message;
    } else if (enumString == NotificationType.block.toString()) {
      return NotificationType.block;
    }
    return NotificationType.general;
  }
}
