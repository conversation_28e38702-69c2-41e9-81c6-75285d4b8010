class SignUpBody {
  String? fName;
  String? lName;
  String? phone;
  String? email;
  String? password;
  String? deviceToken;
  String? zoneId;
  String? companyName;
  String? outletName;
  String? eidExpireAt;
  String? city;

  SignUpBody({
    this.fName,
    this.lName,
    this.phone,
    this.email = '',
    this.password,
    this.deviceToken,
    this.zoneId,
    this.companyName,
    this.outletName,
    this.eidExpireAt,
    this.city,
  });

  SignUpBody.fromJson(Map<String, dynamic> json) {
    fName = json['f_name']?.toString();
    lName = json['l_name']?.toString();
    phone = json['phone']?.toString();
    email = json['email']?.toString();
    password = json['password']?.toString();
    deviceToken = json['device_token']?.toString();
    zoneId = json['zone_id']?.toString();
    companyName = json['company_name']?.toString();
    outletName = json['outlet_name']?.toString();
    eidExpireAt = json['eid_expire_at']?.toString();
    city = json['city']?.toString();
  }

  Map<String, String> toJson() {
    final Map<String, String> data = <String, String>{};
    data['f_name'] = fName ?? '';
    data['l_name'] = lName ?? '';
    data['phone'] = phone ?? '';
    data['email'] = email ?? '';
    data['password'] = password ?? '';
    data['device_token'] = deviceToken ?? '';
    data['zone_id'] = zoneId ?? '';
    data['company_name'] = companyName ?? '';
    data['outlet_name'] = outletName ?? '';
    // data['eid_expire_at'] = this.eidExpireAt;
    data['city'] = city ?? '';
    return data;
  }
}
