import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/shared/rest_api/api_request.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/category_model.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/product_model.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/restaurant_model.dart';

final class CategoryRepository {
  /// Category repository constructor
  const CategoryRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// Get category list
  Future<ApiResult<List<CategoryModel>>> getCategoryList(
    ApiRequest request,
  ) {
    return DioRequest<List<CategoryModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<CategoryModel>(
            (e) => CategoryModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get sub category list
  Future<ApiResult<List<CategoryModel>>> getSubCategoryList(
    ApiRequest request,
  ) {
    return DioRequest<List<CategoryModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<CategoryModel>(
            (e) => CategoryModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get category product list
  Future<ApiResult<List<Product>>> getCategoryProductList(
    ApiRequest request,
  ) {
    return DioRequest<List<Product>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<Product>((e) => Product.fromJson(e as Map<String, dynamic>))
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get category restaurant list
  Future<ApiResult<List<Restaurant>>> getCategoryRestaurantList(
    ApiRequest request,
  ) {
    return DioRequest<List<Restaurant>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<Restaurant>(
            (e) => Restaurant.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get search data
  Future<ApiResult<Map<String, dynamic>>> getSearchData(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Save user interests
  Future<ApiResult<Map<String, dynamic>>> saveUserInterests(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }
}
