import 'package:staffmeal_user_v2/shared/rest_api/model/response/address_model.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/restaurant_model.dart';

import 'order_details_model.dart';

class PaginatedOrderModel {
  int? totalSize;
  String? limit;
  String? offset;
  List<OrderModel>? orders;

  PaginatedOrderModel({this.totalSize, this.limit, this.offset, this.orders});

  PaginatedOrderModel.fromJson(Map<String, dynamic> json) {
    totalSize = json['total_size'] == null
        ? 0
        : int.parse(json['total_size'].toString());
    limit = json['limit'] == null ? '' : json['limit'].toString();
    offset = json['offset'] == null ? '' : json['offset'].toString();
    if (json['orders'] != null) {
      orders = [];
      json['orders'].forEach((dynamic v) {
        orders?.add(OrderModel.fromJson(v as Map<String, dynamic>));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['total_size'] = totalSize;
    data['limit'] = limit;
    data['offset'] = offset;
    if (orders != null) {
      data['orders'] = orders?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class OrderModel {
  String? id;
  int? userId;
  double? orderAmount;
  double? couponDiscountAmount;
  String? couponDiscountTitle;
  String? paymentStatus;
  String? orderStatus;
  double? totalTaxAmount;
  double? totalConvenienceFee;
  String? paymentMethod;
  String? couponCode;
  String? orderNote;
  String? orderType;
  String? createdAt;
  String? updatedAt;
  double? deliveryCharge;
  String? scheduleAt;
  String? otp;
  String? pending;
  String? accepted;
  String? confirmed;
  String? processing;
  String? handover;
  String? pickedUp;
  String? delivered;
  String? canceled;
  String? refundRequested;
  String? refunded;
  int? scheduled;
  double? restaurantDiscountAmount;
  String? failed;
  Tables? table;
  int? detailsCount;
  double? dmTips;
  int? processingTime;
  int? personCount;
  DeliveryMan? deliveryMan;
  Restaurant? restaurant;
  AddressModel? deliveryAddress;
  List<OrderDetailsModel>? details;

  OrderModel({
    this.id,
    this.userId,
    this.orderAmount,
    this.couponDiscountAmount,
    this.couponDiscountTitle,
    this.paymentStatus,
    this.orderStatus,
    this.totalTaxAmount,
    this.totalConvenienceFee,
    this.paymentMethod,
    this.couponCode,
    this.orderNote,
    this.orderType,
    this.createdAt,
    this.updatedAt,
    this.deliveryCharge,
    this.scheduleAt,
    this.otp,
    this.pending,
    this.accepted,
    this.confirmed,
    this.processing,
    this.handover,
    this.pickedUp,
    this.delivered,
    this.canceled,
    this.refundRequested,
    this.refunded,
    this.scheduled,
    this.restaurantDiscountAmount,
    this.failed,
    this.dmTips,
    this.processingTime,
    this.detailsCount,
    this.deliveryMan,
    this.deliveryAddress,
    this.personCount,
    this.restaurant,
    this.table,
    this.details,
  });

  OrderModel.fromJson(Map<String, dynamic> json) {
    id = json['id'] == null ? '' : json['id'].toString();
    userId = json['user_id'] == null
        ? 0
        : int.parse(json['user_id'].toString());
    orderAmount = json['order_amount'] == null
        ? 0
        : double.parse(json['order_amount'].toString());
    couponDiscountAmount = json['coupon_discount_amount'] == null
        ? 0
        : double.parse(json['coupon_discount_amount'].toString());
    couponDiscountTitle = json['coupon_discount_title'] == null
        ? ''
        : json['coupon_discount_title'].toString();
    paymentStatus = json['payment_status'] == null
        ? ''
        : json['payment_status'].toString();
    orderStatus = json['order_status'] == null
        ? ''
        : json['order_status'].toString();
    totalTaxAmount = json['total_tax_amount'] == null
        ? 0
        : double.parse(json['total_tax_amount'].toString());
    totalConvenienceFee = json['total_convenience_fee'] == null
        ? 0
        : double.parse(json['total_convenience_fee'].toString());
    paymentMethod = json['payment_method'] == null
        ? ''
        : json['payment_method'].toString();
    couponCode = json['coupon_code'] == null
        ? ''
        : json['coupon_code'].toString();
    orderNote = json['order_note'] == null ? '' : json['order_note'].toString();
    orderType = json['order_type'] == null ? '' : json['order_type'].toString();
    createdAt = json['created_at'] == null ? '' : json['created_at'].toString();
    updatedAt = json['updated_at'] == null ? '' : json['updated_at'].toString();
    deliveryCharge = json['delivery_charge'] == null
        ? 0
        : double.parse(json['delivery_charge'].toString());
    scheduleAt = json['schedule_at'] == null
        ? ''
        : json['schedule_at'].toString();
    otp = json['otp'] == null ? '' : json['otp'].toString();
    pending = json['pending'] == null ? '' : json['pending'].toString();
    accepted = json['accepted'] == null ? '' : json['accepted'].toString();
    confirmed = json['confirmed'] == null ? '' : json['confirmed'].toString();
    processing = json['processing'] == null
        ? ''
        : json['processing'].toString();
    handover = json['handover'] == null ? '' : json['handover'].toString();
    pickedUp = json['picked_up'] == null ? '' : json['picked_up'].toString();
    delivered = json['delivered'] == null ? '' : json['delivered'].toString();
    canceled = json['canceled'] == null ? '' : json['canceled'].toString();
    refundRequested = json['refund_requested'] == null
        ? ''
        : json['refund_requested'].toString();
    refunded = json['refunded'] == null ? '' : json['refunded'].toString();
    scheduled = json['scheduled'] == null
        ? 0
        : int.parse(json['scheduled'].toString());
    dmTips = json['dm_tips'].toDouble();
    restaurantDiscountAmount = json['restaurant_discount_amount'].toDouble();
    failed = json['failed'] == null ? '' : json['failed'].toString();
    detailsCount = json['details_count'] == null
        ? 0
        : int.parse(json['details_count'].toString());
    table = json['dine_in_schedule'] != null
        ? Tables(
            id: json['dine_in_schedule']['dine']['id'],
            title: json['dine_in_schedule']['dine']['title'],
            no: json['dine_in_schedule']['dine']['no'],
          )
        : null;
    processingTime = json['processing_time'] == null
        ? 0
        : int.parse(json['processing_time'].toString());
    personCount = json['person_count'] == null
        ? 0
        : int.parse(json['person_count'].toString());
    deliveryMan = json['delivery_man'] != null
        ? DeliveryMan.fromJson(json['delivery_man'])
        : null;
    restaurant = json['restaurant'] != null
        ? Restaurant.fromJson(json['restaurant'])
        : null;
    deliveryAddress = json['delivery_address'] != null
        ? AddressModel.fromJson(json['delivery_address'])
        : null;
    if (json['details'] != null) {
      details = [];
      json['details'].forEach((dynamic v) {
        details?.add(OrderDetailsModel.fromJson(v as Map<String, dynamic>));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['order_amount'] = orderAmount;
    data['coupon_discount_amount'] = couponDiscountAmount;
    data['coupon_discount_title'] = couponDiscountTitle;
    data['payment_status'] = paymentStatus;
    data['order_status'] = orderStatus;
    data['total_tax_amount'] = totalTaxAmount;
    data['payment_method'] = paymentMethod;
    data['coupon_code'] = couponCode;
    data['order_note'] = orderNote;
    data['order_type'] = orderType;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['delivery_charge'] = deliveryCharge;
    data['schedule_at'] = scheduleAt;
    data['otp'] = otp;
    data['pending'] = pending;
    data['accepted'] = accepted;
    data['confirmed'] = confirmed;
    data['processing'] = processing;
    data['handover'] = handover;
    data['picked_up'] = pickedUp;
    data['delivered'] = delivered;
    data['canceled'] = canceled;
    data['refund_requested'] = refundRequested;
    data['refunded'] = refunded;
    data['scheduled'] = scheduled;
    data['restaurant_discount_amount'] = restaurantDiscountAmount;
    data['failed'] = failed;
    data['dm_tips'] = dmTips;
    if (table != null) {
      data['dine_in_schedule'] = table?.toJson();
    }
    data['processing_time'] = processingTime;
    data['details_count'] = detailsCount;
    if (deliveryMan != null) {
      data['delivery_man'] = deliveryMan?.toJson();
    }
    if (restaurant != null) {
      data['restaurant'] = restaurant?.toJson();
    }
    if (deliveryAddress != null) {
      data['delivery_address'] = deliveryAddress?.toJson();
    }
    if (details != null) {
      data['details'] = details?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DeliveryMan {
  int? id;
  String? fName;
  String? lName;
  String? phone;
  String? email;
  String? image;
  int? zoneId;
  int? active;
  int? available;
  double? avgRating;
  int? ratingCount;
  String? lat;
  String? lng;
  String? location;

  DeliveryMan({
    this.id,
    this.fName,
    this.lName,
    this.phone,
    this.email,
    this.image,
    this.zoneId,
    this.active,
    this.available,
    this.avgRating,
    this.ratingCount,
    this.lat,
    this.lng,
    this.location,
  });

  DeliveryMan.fromJson(Map<String, dynamic> json) {
    id = json['id'] == null ? 0 : int.parse(json['id'].toString());
    fName = json['f_name'] == null ? '' : json['f_name'].toString();
    lName = json['l_name'] == null ? '' : json['l_name'].toString();
    phone = json['phone'] == null ? '' : json['phone'].toString();
    email = json['email'] == null ? '' : json['email'].toString();
    image = json['image'] == null ? '' : json['image'].toString();
    zoneId = json['zone_id'] == null
        ? 0
        : int.parse(json['zone_id'].toString());
    active = json['active'] == null ? 0 : int.parse(json['active'].toString());
    available = json['available'] == null
        ? 0
        : int.parse(json['available'].toString());
    avgRating = json['avg_rating'] == null
        ? 0
        : double.parse(json['avg_rating'].toString());
    ratingCount = json['rating_count'] == null
        ? 0
        : int.parse(json['rating_count'].toString());
    lat = json['lat'] == null ? '' : json['lat'].toString();
    lng = json['lng'] == null ? '' : json['lng'].toString();
    location = json['location'] == null ? '' : json['location'].toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['f_name'] = fName;
    data['l_name'] = lName;
    data['phone'] = phone;
    data['email'] = email;
    data['image'] = image;
    data['zone_id'] = zoneId;
    data['active'] = active;
    data['available'] = available;
    data['avg_rating'] = avgRating;
    data['rating_count'] = ratingCount;
    data['lat'] = lat;
    data['lng'] = lng;
    data['location'] = location;
    return data;
  }
}
