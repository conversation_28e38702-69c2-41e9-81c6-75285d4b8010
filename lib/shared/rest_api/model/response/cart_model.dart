import 'dart:developer';

import 'package:staffmeal_user_v2/shared/rest_api/model/response/product_model.dart';

class CartModel {
  double? _price;
  double? _discountedPrice;
  List<Variation>? _variation;
  double? _discountAmount;
  int? _quantity;
  List<AddOns>? _addOns;
  bool? _isCampaign;
  Product? _product;

  CartModel(
    double? price,
    double? discountedPrice,
    List<Variation>? variation,
    double? discountAmount,
    int? quantity,
    List<AddOns>? addOns,
    bool? isCampaign,
    Product? product,
  ) {
    _price = price;
    _discountedPrice = discountedPrice;
    _variation = variation;
    _discountAmount = discountAmount;
    _quantity = quantity;
    _addOns = addOns;
    _isCampaign = isCampaign;
    _product = product;
  }

  double? get price => _price;
  double? get discountedPrice => _discountedPrice;
  List<Variation>? get variation => _variation;
  double? get discountAmount => _discountAmount;
  int? get quantity => _quantity;
  set quantity(int? qty) => _quantity = qty;
  List<AddOns>? get addOns => _addOns;
  bool? get isCampaign => _isCampaign;
  Product? get product => _product;

  CartModel.fromJson(Map<String, dynamic> json) {
    log('_quantity $_quantity');
    _price = json['price'] == null ? 0 : double.parse(json['price'].toString());
    _discountedPrice = json['discounted_price'] == null
        ? 0
        : double.parse(json['discounted_price'].toString());
    if (json['variation'] != null) {
      _variation = [];
      json['variation'].forEach((v) {
        _variation?.add(Variation.fromJson(v));
      });
    }
    _discountAmount = json['discount_amount']?.toDouble();
    _quantity = json['quantity'] == null ? 0 : json['quantity'];
    if (json['add_ons'] != null) {
      _addOns = [];
      json['add_ons'].forEach((v) {
        _addOns?.add(AddOns.fromJson(v));
      });
    }
    _isCampaign = json['is_campaign'];
    if (json['product'] != null) {
      _product = Product.fromJson(json['product']);
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['price'] = _price ?? 0;
    data['discounted_price'] = _discountedPrice;
    if (_variation != null) {
      data['variation'] = _variation?.map((v) => v.toJson()).toList();
    }
    data['discount_amount'] = _discountAmount;
    data['quantity'] = _quantity ?? 0;
    if (_addOns != null) {
      data['add_ons'] = _addOns?.map((v) => v.toJson()).toList();
    }
    data['is_campaign'] = _isCampaign;
    data['product'] = _product?.toJson();
    return data;
  }
}
