import 'package:flutter/material.dart';

class AddressDetails extends StatelessWidget {
  final AddressModel addressDetails;
  const AddressDetails({
    Key? key,
    required this.addressDetails,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            addressDetails.address ?? '',
            style: robotoRegular.copyWith(fontSize: Dimensions.fontSizeSmall),
            maxLines: 4,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 5),
          Wrap(children: [
            (addressDetails.road != null)
                ? Text(
                    '${'street_number'.tr}: ${addressDetails.road}${(addressDetails.house != null) ? ',' : ' '}',
                    style: robotoRegular.copyWith(
                        fontSize: Dimensions.fontSizeExtraSmall),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  )
                : const SizedBox(),
            (addressDetails.house != null)
                ? Text(
                    '${'house'.tr}: ${addressDetails.house}${(addressDetails.floor != null) ? ',' : ' '}',
                    style: robotoRegular.copyWith(
                        fontSize: Dimensions.fontSizeExtraSmall),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  )
                : const SizedBox(),
            (addressDetails.floor != null)
                ? Text(
                    '${'floor'.tr}: ${addressDetails.floor}',
                    style: robotoRegular.copyWith(
                        fontSize: Dimensions.fontSizeExtraSmall),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  )
                : const SizedBox(),
          ]),
        ]);
  }
}
