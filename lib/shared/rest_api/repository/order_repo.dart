import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/shared/rest_api/api_request.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/order_model.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/order_details_model.dart';

final class OrderRepository {
  /// Order repository constructor
  const OrderRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// Get running order list
  Future<ApiResult<List<OrderModel>>> getRunningOrderList(
    ApiRequest request,
  ) {
    return DioRequest<List<OrderModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<OrderModel>(
            (e) => OrderModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get history order list
  Future<ApiResult<List<OrderModel>>> getHistoryOrderList(
    ApiRequest request,
  ) {
    return DioRequest<List<OrderModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<OrderModel>(
            (e) => OrderModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get order details
  Future<ApiResult<OrderDetailsModel>> getOrderDetails(
    ApiRequest request,
  ) {
    return DioRequest<OrderDetailsModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: OrderDetailsModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Cancel order
  Future<ApiResult<Map<String, dynamic>>> cancelOrder(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// Track order
  Future<ApiResult<OrderModel>> trackOrder(
    ApiRequest request,
  ) {
    return DioRequest<OrderModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: OrderModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Place order
  Future<ApiResult<Map<String, dynamic>>> placeOrder(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// Get delivery man data
  Future<ApiResult<Map<String, dynamic>>> getDeliveryManData(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Switch to COD
  Future<ApiResult<Map<String, dynamic>>> switchToCOD(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }
}
