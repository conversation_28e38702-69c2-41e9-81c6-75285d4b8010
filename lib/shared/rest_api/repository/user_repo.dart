import 'package:get/get.dart';
import 'package:get/get_connect/http/src/response/response.dart';
import 'package:image_picker/image_picker.dart';
import 'package:efood_multivendor/data/api/api_client.dart';
import 'package:efood_multivendor/data/model/response/userinfo_model.dart';
import 'package:efood_multivendor/util/app_constants.dart';

class UserRepo {
  final ApiClient apiClient;
  UserRepo({required this.apiClient});

  Future<Response> getUserInfo() async {
    return await apiClient.getData(AppConstants.CUSTOMER_INFO_URI);
  }

  Future<Response> updateProfile(
      UserInfoModel userInfoModel, XFile data, String token) async {
    Map<String, String> body = {};
    body.addAll(<String, String>{
      'f_name': userInfoModel.fName ?? '',
      'l_name': userInfoModel.lName ?? '',
      'email': userInfoModel.email ?? '',
      'phone': userInfoModel.phone ?? '',
      'city': userInfoModel.city ?? '',
    });
    return await apiClient.postMultipartData(
        AppConstants.UPDATE_PROFILE_URI, body, [MultipartBody('image', data)]);
  }

  Future<Response> updateUserDocuments(
      Map<String, String> body, List<MultipartBody> multiParts) async {
    return await apiClient.postMultipartData(
        AppConstants.UPDATE_PROFILE_URI, body, multiParts);
  }

  Future<Response> changePassword(UserInfoModel userInfoModel) async {
    return await apiClient.postData(AppConstants.UPDATE_PROFILE_URI, {
      'f_name': userInfoModel.fName,
      'l_name': userInfoModel.lName,
      'email': userInfoModel.email,
      'password': userInfoModel.password,
      'phone': userInfoModel.phone
    });
  }

  Future<Response> deleteUser() async {
    return await apiClient.deleteData(AppConstants.CUSTOMER_REMOVE);
  }

  Future<Response> forgetPassword(UserInfoModel userInfoModel) async {
    // Map<String, String> _body;
    // if(email != null) {
    //   _body = {"email": email};
    // }else {
    //   _body = {"phone": phone};
    // }
    return await apiClient.postData(
        AppConstants.UPDATE_PROFILE_URI, userInfoModel);
  }

  Future<Response> profileVerifyPhone(
      String phone, String? email, String otp) async {
    Map<String, String> body;
    if (email != null) {
      body = {"phone": phone, "email": email, "otp": otp};
    } else {
      body = {"phone": phone, "otp": otp};
    }
    return await apiClient.postData(
        AppConstants.PROFILE_VERIFY_PHONE_URI, body);
  }

  Future<Response> profileVerifyEmail(String email, String token) async {
    return await apiClient.postData(AppConstants.PROFILE_VERIFY_EMAIL_URI,
        {"email": email, "token": token});
  }
}
