import 'package:google_maps_flutter/google_maps_flutter.dart';

class ZoneModel {
  int? id;
  String? name;
  Coordinates? coordinates;
  int? status;
  String? createdAt;
  String? updatedAt;
  String? restaurantWiseTopic;
  String? customerWiseTopic;
  String? deliverymanWiseTopic;
  double? minimumShippingCharge;
  double? perKmShippingCharge;

  ZoneModel(
      {this.id,
      this.name,
      this.coordinates,
      this.status,
      this.createdAt,
      this.updatedAt,
      this.restaurantWiseTopic,
      this.customerWiseTopic,
      this.deliverymanWiseTopic,
      this.minimumShippingCharge,
      this.perKmShippingCharge});

  ZoneModel.fromJson(Map<String, dynamic> json) {
    id = json['id'] == null ? 0 : int.parse(json['id'].toString());
    name = json['name'] == null ? '' : json['name'].toString();
    coordinates = json['coordinates'] != null
        ? Coordinates.fromJson(json['coordinates'])
        : null;
    status = json['status'] == null ? 0 : int.parse(json['status'].toString());
    createdAt = json['created_at'] == null ? '' : json['created_at'].toString();
    updatedAt = json['updated_at'] == null ? '' : json['updated_at'].toString();
    restaurantWiseTopic = json['restaurant_wise_topic'] == null
        ? ''
        : json['restaurant_wise_topic'].toString();
    customerWiseTopic = json['customer_wise_topic'] == null
        ? ''
        : json['customer_wise_topic'].toString();
    deliverymanWiseTopic = json['deliveryman_wise_topic'] == null
        ? ''
        : json['deliveryman_wise_topic'].toString();
    minimumShippingCharge = json['minimum_shipping_charge'] == null
        ? 0
        : double.parse(json['minimum_shipping_charge'].toString());
    perKmShippingCharge = json['per_km_shipping_charge'] == null
        ? 0
        : double.parse(json['per_km_shipping_charge'].toString());
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    if (coordinates != null) {
      data['coordinates'] = coordinates?.toJson();
    }
    data['status'] = status;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['restaurant_wise_topic'] = restaurantWiseTopic;
    data['customer_wise_topic'] = customerWiseTopic;
    data['deliveryman_wise_topic'] = deliverymanWiseTopic;
    data['minimum_shipping_charge'] = minimumShippingCharge;
    data['per_km_shipping_charge'] = perKmShippingCharge;
    return data;
  }
}

class Coordinates {
  String? type;
  List<LatLng>? coordinates;

  Coordinates({this.type, this.coordinates});

  Coordinates.fromJson(Map<String, dynamic> json) {
    type = json['type'] == null ? '' : json['type'].toString();
    if (json['coordinates'] != null) {
      coordinates = <LatLng>[];
      json['coordinates'][0].forEach((v) {
        coordinates?.add(LatLng(
            double.parse(v[0].toString()), double.parse(v[1].toString())));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    if (coordinates != null) {
      data['coordinates'] = coordinates?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
