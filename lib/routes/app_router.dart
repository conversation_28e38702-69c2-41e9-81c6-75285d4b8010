import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// root navigation key
final GlobalKey<NavigatorState> rootNavKey = GlobalKey<NavigatorState>(debugLabel: 'root');

/// Scaffold navigation key
final GlobalKey<ScaffoldMessengerState> sfMessengerKey = GlobalKey<ScaffoldMessengerState>(debugLabel: 'appScaffold');

/// current route
String? currentRoute;

/// global GoRouter instance which has all page routes
final appRouter = GoRouter(
  navigatorKey: rootNavKey,
  redirect: (context, state) {
    currentRoute = state.fullPath;
    switch (state.fullPath) {
      case '/':
        // return Injector.instance<AppDB>().isLogin ? '/${AppRoutes.bottomRoute}' : '/${AppRoutes.createAccount}';
        return '/';
    }
    return null;
  },
  routes: [
    GoRoute(
      path: '/',
      pageBuilder: (context, state) => MaterialPage(
        key: state.pageKey,
        child: const Scaffold(
          body: Center(
            child: Text('Route not found'),
          ),
        ),
      ),
    ),
  ],
);
