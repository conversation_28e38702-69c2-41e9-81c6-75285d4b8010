import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/shared/rest_api/api_request.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/product_model.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/restaurant_model.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/review_model.dart';

final class RestaurantRepository {
  /// Restaurant repository constructor
  const RestaurantRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// Get restaurant list
  Future<ApiResult<List<Restaurant>>> getRestaurantList(
    ApiRequest request,
  ) {
    return DioRequest<List<Restaurant>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<Restaurant>(
            (e) => Restaurant.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get popular restaurant list
  Future<ApiResult<List<Restaurant>>> getPopularRestaurantList(
    ApiRequest request,
  ) {
    return DioRequest<List<Restaurant>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<Restaurant>(
            (e) => Restaurant.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get latest restaurant list
  Future<ApiResult<List<Restaurant>>> getLatestRestaurantList(
    ApiRequest request,
  ) {
    return DioRequest<List<Restaurant>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<Restaurant>(
            (e) => Restaurant.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get restaurant details
  Future<ApiResult<Restaurant>> getRestaurantDetails(
    ApiRequest request,
  ) {
    return DioRequest<Restaurant>(
      dio: dio,
      path: request.path!,
      jsonMapper: Restaurant.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get restaurant product list
  Future<ApiResult<List<Product>>> getRestaurantProductList(
    ApiRequest request,
  ) {
    return DioRequest<List<Product>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<Product>((e) => Product.fromJson(e as Map<String, dynamic>))
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get restaurant search product list
  Future<ApiResult<List<Product>>> getRestaurantSearchProductList(
    ApiRequest request,
  ) {
    return DioRequest<List<Product>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<Product>((e) => Product.fromJson(e as Map<String, dynamic>))
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get restaurant review list
  Future<ApiResult<List<ReviewModel>>> getRestaurantReviewList(
    ApiRequest request,
  ) {
    return DioRequest<List<ReviewModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<ReviewModel>(
            (e) => ReviewModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }
}
