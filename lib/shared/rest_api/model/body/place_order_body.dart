import 'package:staffmeal_user_v2/shared/rest_api/model/response/product_model.dart';

class PlaceOrderBody {
  List<Cart>? _cart;
  double? _couponDiscountAmount;
  String? _couponDiscountTitle;
  double? _orderAmount;
  String? _orderType;
  String? _paymentMethod;
  String? _orderNote;
  String? _couponCode;
  int? _restaurantId;
  double? _distance;
  String? _scheduleAt;
  String? _address;
  String? _latitude;
  String? _longitude;
  String? _contactPersonName;
  String? _contactPersonNumber;
  String? _addressType;
  String? _road;
  String? _house;
  String? _floor;
  String? _dmTips;
  String? _personNumber;
  bool? _pickUpDineIn;

  PlaceOrderBody({
    List<Cart>? cart,
    double? couponDiscountAmount,
    String? couponDiscountTitle,
    String? couponCode,
    double? orderAmount,
    String? orderType,
    String? paymentMethod,
    int? restaurantId,
    double? distance,
    String? scheduleAt,
    String? orderNote,
    String? address,
    String? latitude,
    String? longitude,
    String? contactPersonName,
    String? contactPersonNumber,
    String? addressType,
    String? road,
    String? house,
    String? floor,
    String? dmTips,
    String? personNumber,
    bool? pickUpDineIn,
  }) {
    _cart = cart;
    _couponDiscountAmount = couponDiscountAmount;
    _couponDiscountTitle = couponDiscountTitle;
    _orderAmount = orderAmount;
    _orderType = orderType;
    _paymentMethod = paymentMethod;
    _orderNote = orderNote;
    _couponCode = couponCode;
    _restaurantId = restaurantId;
    _distance = distance;
    _scheduleAt = scheduleAt;
    _address = address;
    _latitude = latitude;
    _longitude = longitude;
    _contactPersonName = contactPersonName;
    _contactPersonNumber = contactPersonNumber;
    _addressType = addressType;
    _road = road;
    _house = house;
    _floor = floor;
    _dmTips = dmTips;
    _personNumber = personNumber;
    _pickUpDineIn = pickUpDineIn;
  }

  List<Cart>? get cart => _cart;
  double? get couponDiscountAmount => _couponDiscountAmount;
  String? get couponDiscountTitle => _couponDiscountTitle;
  double? get orderAmount => _orderAmount;
  String? get orderType => _orderType;
  String? get paymentMethod => _paymentMethod;
  String? get orderNote => _orderNote;
  String? get couponCode => _couponCode;
  int? get restaurantId => _restaurantId;
  double? get distance => _distance;
  String? get scheduleAt => _scheduleAt;
  String? get address => _address;
  String? get latitude => _latitude;
  String? get longitude => _longitude;
  String? get contactPersonName => _contactPersonName;
  String? get contactPersonNumber => _contactPersonNumber;
  String? get road => _road;
  String? get house => _house;
  String? get floor => _floor;
  String? get dmTips => _dmTips;
  String? get personNumber => _personNumber;
  bool? get pickUpDineIn => _pickUpDineIn;

  PlaceOrderBody.fromJson(Map<String, dynamic> json) {
    if (json['cart'] != null) {
      _cart = [];
      json['cart'].forEach((dynamic v) {
        _cart?.add(Cart.fromJson(v as Map<String, dynamic>));
      });
    }
    _couponDiscountAmount = json['coupon_discount_amount'] != null
        ? double.tryParse(json['coupon_discount_amount'].toString())
        : null;
    _couponDiscountTitle = json['coupon_discount_title']?.toString();
    _orderAmount = json['order_amount'] != null
        ? double.tryParse(json['order_amount'].toString())
        : null;
    _orderType = json['order_type']?.toString();
    _paymentMethod = json['payment_method']?.toString();
    _orderNote = json['order_note']?.toString();
    _couponCode = json['coupon_code']?.toString();
    _restaurantId = json['restaurant_id'] != null
        ? int.tryParse(json['restaurant_id'].toString())
        : null;
    _distance = json['distance'] != null
        ? double.tryParse(json['distance'].toString())
        : null;
    _scheduleAt = json['schedule_at']?.toString();
    _address = json['address']?.toString();
    _latitude = json['latitude']?.toString();
    _longitude = json['longitude']?.toString();
    _contactPersonName = json['contact_person_name']?.toString();
    _contactPersonNumber = json['contact_person_number']?.toString();
    _addressType = json['address_type']?.toString();
    _road = json['road']?.toString();
    _house = json['house']?.toString();
    _floor = json['floor']?.toString();
    _dmTips = json['dm_tips']?.toString();
    _personNumber = json['person_count']?.toString();
    _pickUpDineIn = json['pickup_dine_in'] != null
        ? json['pickup_dine_in'] as bool
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (_cart != null) {
      data['cart'] = _cart?.map((v) => v.toJson()).toList();
    }
    data['coupon_discount_amount'] = _couponDiscountAmount;
    data['coupon_discount_title'] = _couponDiscountTitle;
    data['order_amount'] = _orderAmount;
    data['order_type'] = _orderType;
    data['payment_method'] = _paymentMethod;
    data['order_note'] = _orderNote;
    data['coupon_code'] = _couponCode;
    data['restaurant_id'] = _restaurantId;
    data['distance'] = _distance;
    data['schedule_at'] = _scheduleAt;
    data['address'] = _address;
    data['latitude'] = _latitude;
    data['longitude'] = _longitude;
    data['contact_person_name'] = _contactPersonName;
    data['contact_person_number'] = _contactPersonNumber;
    data['address_type'] = _addressType;
    data['road'] = _road;
    data['house'] = _house;
    data['floor'] = _floor;
    data['dm_tips'] = _dmTips;
    data['person_count'] = _personNumber;
    data['pickup_dine_in'] = _pickUpDineIn;
    return data;
  }
}

class Cart {
  int? _foodId;
  int? _itemCampaignId;
  String? _price;
  String? _variant;
  List<Variation>? _variation;
  int? _quantity;
  List<AddOns>? _addOns;

  Cart(
    int? foodId,
    int? itemCampaignId,
    String? price,
    String? variant,
    List<Variation>? variation,
    int? quantity,
    List<AddOns>? addOns,
  ) {
    _foodId = foodId;
    _itemCampaignId = itemCampaignId;
    _price = price;
    _variant = variant;
    _variation = variation;
    _quantity = quantity;
    _addOns = addOns;
  }

  int? get foodId => _foodId;
  int? get itemCampaignId => _itemCampaignId;
  String? get price => _price;
  String? get variant => _variant;
  List<Variation>? get variation => _variation;
  int? get quantity => _quantity;
  List<AddOns>? get addOns => _addOns;

  Cart.fromJson(Map<String, dynamic> json) {
    _foodId = json['food_id'] != null
        ? int.tryParse(json['food_id'].toString())
        : null;
    _itemCampaignId = json['item_campaign_id'] != null
        ? int.tryParse(json['item_campaign_id'].toString())
        : null;
    _price = json['price']?.toString();
    _variant = json['variant']?.toString();
    if (json['variation'] != null) {
      _variation = [];
      json['variation'].forEach((dynamic v) {
        _variation?.add(Variation.fromJson(v as Map<String, dynamic>));
      });
    }
    _quantity = json['quantity'] != null
        ? int.tryParse(json['quantity'].toString())
        : null;
    if (json['add_ons'] != null) {
      _addOns = [];
      json['add_ons'].forEach((dynamic v) {
        _addOns?.add(AddOns.fromJson(v as Map<String, dynamic>));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['food_id'] = _foodId;
    data['item_campaign_id'] = _itemCampaignId;
    data['price'] = _price;
    data['variant'] = _variant;
    if (_variation != null) {
      data['variation'] = _variation?.map((v) => v.toJson()).toList();
    }
    data['quantity'] = _quantity;
    if (_addOns != null) {
      data['add_ons'] = _addOns?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
