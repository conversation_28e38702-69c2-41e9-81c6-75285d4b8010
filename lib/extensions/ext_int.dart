import 'dart:math';

/// Extension for [int]
extension IntX on int {
  /// get file size from bytes to "B, KB, MB, GB, TB" format
  String getFileSizeString({int decimals = 1}) {
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (this == 0) return '0 ${suffixes[0]}';
    final i = (log(this) / log(1024)).floor();
    return '${(this / pow(1024, i)).toStringAsFixed(decimals)} ${suffixes[i]}';
  }

  /// covert unread chat count in 99+
  String get compactUnreadCount => this > 99 ? '99+' : toString();
}
