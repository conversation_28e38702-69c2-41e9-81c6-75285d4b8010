import 'package:efood_multivendor/data/model/response/zone_response_model.dart';

class AddressModel {
  int? id;
  String? addressType;
  String? contactPersonNumber;
  String? address;
  String? latitude;
  String? longitude;
  int? zoneId;
  List<int>? zoneIds;
  String? method;
  String? contactPersonName;
  String? road;
  String? house;
  String? floor;
  List<ZoneData>? zoneData;

  AddressModel({
    this.id,
    this.addressType,
    this.contactPersonNumber,
    this.address,
    this.latitude,
    this.longitude,
    this.zoneId,
    this.zoneIds,
    this.method,
    this.contactPersonName,
    this.road,
    this.house,
    this.floor,
    this.zoneData,
  });

  AddressModel.fromJson(Map<String, dynamic> json) {
    id = json['id'] == null ? 0 : int.parse(json['id'].toString());
    addressType =
        json['address_type'] == null ? '' : json['address_type'].toString();
    contactPersonNumber = json['contact_person_number'] == null
        ? ''
        : json['contact_person_number'].toString();
    address = json['address'] == null ? '' : json['address'].toString();
    latitude = json['latitude'] == null ? '' : json['latitude'].toString();
    longitude = json['longitude'] == null ? '' : json['longitude'].toString();
    zoneId =
        json['zone_id'] == null ? 0 : int.parse(json['zone_id'].toString());
    zoneIds = json['zone_ids'] == null
        ? []
        : List<int>.from((json['zone_ids'] as List));
    method = json['_method'] == null ? '' : json['_method'].toString();
    contactPersonName = json['contact_person_name'] == null
        ? ''
        : json['contact_person_name'].toString();
    floor = json['floor'] == null ? '' : json['floor'].toString();
    road = json['road'] == null ? '' : json['road'].toString();
    house = json['house'] == null ? '' : json['house'].toString();
    if (json['zone_data'] != null) {
      zoneData = [];
      json['zone_data'].forEach((v) {
        zoneData!.add(ZoneData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['address_type'] = addressType;
    data['contact_person_number'] = contactPersonNumber;
    data['address'] = address;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['zone_id'] = zoneId;
    data['zone_ids'] = zoneIds;
    data['_method'] = method;
    data['contact_person_name'] = contactPersonName;
    data['road'] = road;
    data['house'] = house;
    data['floor'] = floor;
    if (zoneData != null) {
      data['zone_data'] = zoneData!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
