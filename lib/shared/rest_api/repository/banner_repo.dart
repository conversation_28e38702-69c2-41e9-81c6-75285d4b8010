import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/shared/rest_api/api_request.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/banner_model.dart';

final class BannerRepository {
  /// Banner repository constructor
  const BannerRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// Get banner list
  Future<ApiResult<List<BannerModel>>> getBannerList(
    ApiRequest request,
  ) {
    return DioRequest<List<BannerModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<BannerModel>(
            (e) => BannerModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }
}
