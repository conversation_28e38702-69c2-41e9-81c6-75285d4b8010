import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/shared/rest_api/api_request.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/coupon_model.dart';

final class CouponRepository {
  /// Coupon repository constructor
  const CouponRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// Get coupon list
  Future<ApiResult<List<CouponModel>>> getCouponList(
    ApiRequest request,
  ) {
    return DioRequest<List<CouponModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<CouponModel>(
            (e) => CouponModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Apply coupon
  Future<ApiResult<Map<String, dynamic>>> applyCoupon(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }
}
