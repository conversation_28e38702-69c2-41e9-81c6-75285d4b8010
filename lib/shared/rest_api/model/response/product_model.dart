class ProductModel {
  int? totalSize;
  String? limit;
  int? offset;
  List<Product>? products;

  ProductModel({this.totalSize, this.limit, this.offset, this.products});

  ProductModel.fromJson(Map<String, dynamic> json) {
    totalSize = json['total_size'] == null
        ? 0
        : int.parse(json['total_size'].toString());
    limit = json['limit'] == null ? '' : json['limit'].toString();
    offset =
        (json['offset'] != null && json['offset'].toString().trim().isNotEmpty)
        ? int.parse(json['offset'].toString())
        : null;
    if (json['products'] != null) {
      products = [];
      json['products'].forEach((dynamic v) {
        products?.add(Product.fromJson(v as Map<String, dynamic>));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['total_size'] = totalSize;
    data['limit'] = limit;
    data['offset'] = offset;
    if (products != null) {
      data['products'] = products?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Product {
  int? id;
  String? name;
  String? description;
  String? image;
  int? categoryId;
  List<CategoryIds>? categoryIds;
  List<Variation>? variations;
  List<AddOns>? addOns;
  List<ChoiceOptions>? choiceOptions;
  double? price;
  double? tax;
  double? discount;
  String? discountType;
  String? availableTimeStarts;
  String? availableTimeEnds;
  int? restaurantId;
  String? restaurantName;
  double? restaurantDiscount;
  bool? scheduleOrder;
  double? avgRating;
  int? ratingCount;
  int? veg;

  Product({
    this.id,
    this.name,
    this.description,
    this.image,
    this.categoryId,
    this.categoryIds,
    this.variations,
    this.addOns,
    this.choiceOptions,
    this.price,
    this.tax,
    this.discount,
    this.discountType,
    this.availableTimeStarts,
    this.availableTimeEnds,
    this.restaurantId,
    this.restaurantName,
    this.restaurantDiscount,
    this.scheduleOrder,
    this.avgRating,
    this.ratingCount,
    this.veg,
  });

  Product.fromJson(Map<String, dynamic> json) {
    id = json['id'] == null ? 0 : int.parse(json['id'].toString());
    name = json['name'] == null ? '' : json['name'].toString();
    description = json['description'] == null
        ? ''
        : json['description'].toString();
    image = json['image'] == null ? '' : json['image'].toString();
    categoryId = json['category_id'] == null
        ? 0
        : int.parse(json['category_id'].toString());
    if (json['category_ids'] != null) {
      categoryIds = [];
      json['category_ids'].forEach((dynamic v) {
        categoryIds?.add(CategoryIds.fromJson(v as Map<String, dynamic>));
      });
    }
    if (json['variations'] != null) {
      variations = [];
      json['variations'].forEach((dynamic v) {
        variations?.add(Variation.fromJson(v as Map<String, dynamic>));
      });
    }
    if (json['add_ons'] != null) {
      addOns = <AddOns>[];
      json['add_ons'].forEach((dynamic v) {
        if (v is Map<String, dynamic>) {
          addOns?.add(AddOns.fromJson(v));
        }
      });
    } else {
      addOns = [];
    }
    if (json['choice_options'] != null) {
      choiceOptions = [];
      json['choice_options'].forEach((dynamic v) {
        choiceOptions?.add(ChoiceOptions.fromJson(v as Map<String, dynamic>));
      });
    }
    price = json['price'] == null ? 0 : double.parse(json['price'].toString());
    tax = json['tax'] == null ? 0 : double.parse(json['tax'].toString());
    discount = json['discount'] == null
        ? 0
        : double.parse(json['discount'].toString());
    discountType = json['discount_type'] == null
        ? ''
        : json['discount_type'].toString();
    availableTimeStarts = json['available_time_starts'] == null
        ? ''
        : json['available_time_starts'].toString();
    availableTimeEnds = json['available_time_ends'] == null
        ? ''
        : json['available_time_ends'].toString();
    restaurantId = json['restaurant_id'] == null
        ? 0
        : int.parse(json['restaurant_id'].toString());
    restaurantName = json['restaurant_name'] == null
        ? ''
        : json['restaurant_name'].toString();
    restaurantDiscount = json['restaurant_discount'] == null
        ? 0
        : double.parse(json['restaurant_discount'].toString());
    scheduleOrder = json['schedule_order'] == null
        ? false
        : bool.parse(json['schedule_order'].toString());
    avgRating = json['avg_rating'] == null
        ? 0
        : double.parse(json['avg_rating'].toString());
    ratingCount = json['rating_count'] == null
        ? 0
        : int.parse(json['rating_count'].toString());
    veg = json['veg'] != null ? int.parse(json['veg'].toString()) : 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['description'] = description;
    data['image'] = image;
    data['category_id'] = categoryId;
    if (categoryIds != null) {
      data['category_ids'] = categoryIds?.map((v) => v.toJson()).toList();
    }
    if (variations != null) {
      data['variations'] = variations?.map((v) => v.toJson()).toList();
    }
    if (addOns != null) {
      data['add_ons'] = addOns?.map((v) => v.toJson()).toList();
    }
    if (choiceOptions != null) {
      data['choice_options'] = choiceOptions?.map((v) => v.toJson()).toList();
    }
    data['price'] = price;
    data['tax'] = tax;
    data['discount'] = discount;
    data['discount_type'] = discountType;
    data['available_time_starts'] = availableTimeStarts;
    data['available_time_ends'] = availableTimeEnds;
    data['restaurant_id'] = restaurantId;
    data['restaurant_name'] = restaurantName;
    data['restaurant_discount'] = restaurantDiscount;
    data['schedule_order'] = scheduleOrder;
    data['avg_rating'] = avgRating;
    data['rating_count'] = ratingCount;
    data['veg'] = veg;
    return data;
  }
}

class CategoryIds {
  String? id;

  CategoryIds({this.id});

  CategoryIds.fromJson(Map<String, dynamic> json) {
    id = json['id'] == null ? '' : json['id'].toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    return data;
  }
}

class Variation {
  String? type;
  double? price;

  Variation({this.type, this.price});

  Variation.fromJson(Map<String, dynamic> json) {
    type = json['type'] == null ? '' : json['type'].toString();
    price = json['price'] == null ? 0 : double.parse(json['price'].toString());
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    data['price'] = price;
    return data;
  }
}

class AddOns {
  String? id;
  String? name;
  List<Addons>? addons;
  String? required;
  String? maxQuantity;
  String? minQuantity;
  AddOns({
    this.id,
    this.name,
    this.addons,
    this.required,
    this.maxQuantity,
    this.minQuantity,
  });

  AddOns.fromJson(Map<String, dynamic> json) {
    id = json['id'] == null ? '' : json['id'].toString();
    name = json['name'] == null ? '' : json['name'].toString();
    if (json['addons'] != null) {
      addons = <Addons>[];
      json['addons'].forEach((dynamic v) {
        addons?.add(Addons.fromJson(v as Map<String, dynamic>));
      });
    }
    required = json['required'] == null ? '' : json['required'].toString();
    maxQuantity = json['max_quantity'] == null
        ? ''
        : json['max_quantity'].toString();
    minQuantity = json['min_quantity'] == null
        ? ''
        : json['min_quantity'].toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    if (addons != null) {
      data['addons'] = addons?.map((v) => v.toJson()).toList();
    }
    data['required'] = required;
    data['max_quantity'] = maxQuantity;
    data['min_quantity'] = minQuantity;
    return data;
  }
}

class Addons {
  String? id;
  String? name;
  String? price;
  String? quantity;
  bool? isSelected;

  List<AddonVariations>? addonVariations;

  Addons({this.id, this.name, this.price, this.quantity, this.addonVariations});

  Addons.fromJson(Map<String, dynamic> json) {
    id = json['id'] == null ? '' : json['id'].toString();
    name = json['name'] == null ? '' : json['name'].toString();
    price = json['price'] == null ? '' : json['price'].toString();
    quantity = json['quantity'] == null || json['quantity'].toString() == '0'
        ? '1'
        : json['quantity'].toString();
    isSelected = json['isSelected'] == null
        ? false
        : json['isSelected'] as bool;

    if (json['variations'] != null) {
      addonVariations = <AddonVariations>[];
      json['variations'].forEach((dynamic v) {
        addonVariations?.add(
          AddonVariations.fromJson(v as Map<String, dynamic>),
        );
      });
    } else {
      addonVariations = [];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['price'] = price;
    data['quantity'] = quantity;
    data['isSelected'] = isSelected;

    if (addonVariations != null) {
      data['variations'] = addonVariations?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class AddonVariations {
  String? key;
  String? image;

  AddonVariations({this.key, this.image});

  AddonVariations.fromJson(Map<String, dynamic> json) {
    key = json['key'] == null ? '' : json['key'].toString();
    image = json['image'] == null ? '' : json['image'].toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['key'] = key;
    data['image'] = image;
    return data;
  }
}

class ChoiceOptions {
  String? name;
  String? title;
  List<String>? options;

  ChoiceOptions({this.name, this.title, this.options});

  ChoiceOptions.fromJson(Map<String, dynamic> json) {
    name = json['name'] == null ? '' : json['name'].toString();
    title = json['title'] == null ? '' : json['title'].toString();
    options = json['options'] == null
        ? []
        : List<String>.from((json['options'] as List<dynamic>));
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['name'] = name;
    data['title'] = title;
    data['options'] = options;
    return data;
  }
}

class AddonIds {
  int? id;
  String? name;
  double? price;
  int? categoryId;
  List<AddonVariations>? addonVariations;

  AddonIds({
    this.id,
    this.name,
    this.price,
    this.categoryId,
    this.addonVariations,
  });

  AddonIds.fromJson(Map<String, dynamic> json) {
    id = json['id'] == null ? 0 : int.parse(json['id'].toString());
    name = json['name'] == null ? '' : json['name'].toString();
    price = json['price'] == null ? 0 : double.parse(json['price'].toString());
    categoryId = json['category_id'] == null
        ? 0
        : int.parse(json['category_id'].toString());
    if (json['variations'] != null) {
      addonVariations = <AddonVariations>[];
      json['variations'].forEach((dynamic v) {
        addonVariations?.add(
          AddonVariations.fromJson(v as Map<String, dynamic>),
        );
      });
    } else {
      addonVariations = [];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['price'] = price;
    data['category_id'] = categoryId;
    if (addonVariations != null) {
      data['variations'] = addonVariations?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
