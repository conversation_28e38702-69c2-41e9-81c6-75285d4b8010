import 'package:staffmeal_user_v2/shared/rest_api/model/response/conversation_model.dart';

class UserInfoModel {
  int? id;
  String? fName;
  String? lName;
  String? email;
  String? image;
  String? phone;
  String? password;
  int? orderCount;
  int? memberSinceDays;
  double? walletBalance;
  int? loyaltyPoint;
  String? refCode;
  int? zoneId;
  User? userInfo;
  String? city;
  UserData? userData;

  UserInfoModel({
    this.id,
    this.fName,
    this.lName,
    this.email,
    this.image,
    this.phone,
    this.password,
    this.orderCount,
    this.memberSinceDays,
    this.walletBalance,
    this.loyaltyPoint,
    this.refCode,
    this.zoneId,
    this.userInfo,
    this.city,
    this.userData,
  });

  UserInfoModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    fName = json['f_name'];
    lName = json['l_name'];
    email = json['email'];
    image = json['image'];
    phone = json['phone'];
    password = json['password'];
    orderCount = json['order_count'];
    memberSinceDays = json['member_since_days'];
    if (json['wallet_balance'] != null) {
      walletBalance = json['wallet_balance'].toDouble();
    }
    loyaltyPoint = json['loyalty_point'];
    refCode = json['ref_code'];
    zoneId = json['zone_id'];
    userInfo = json['userinfo'] != null
        ? User.fromJson(json['userinfo'])
        : null;
    userData = json['user_data'] != null
        ? UserData.fromJson(json['user_data'])
        : null;
    city = json['city'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['f_name'] = fName;
    data['l_name'] = lName;
    data['email'] = email;
    data['image'] = image;
    data['phone'] = phone;
    data['password'] = password;
    data['order_count'] = orderCount;
    data['member_since_days'] = memberSinceDays;
    data['wallet_balance'] = walletBalance;
    data['loyalty_point'] = loyaltyPoint;
    data['ref_code'] = refCode;
    data['zone_id'] = zoneId;
    if (userInfo != null) {
      data['user`info'] = userInfo!.toJson();
    }
    data['city'] = city;
    return data;
  }
}

class UserData {
  int? id;
  int? userId;
  String? emiratesIdFront;
  String? emiratesIdBack;
  String? visaCopy;
  DateTime? eidExpireAt;
  String? companyName;
  String? outletName;

  UserData({
    this.id,
    this.userId,
    this.emiratesIdFront,
    this.emiratesIdBack,
    this.visaCopy,
    this.eidExpireAt,
    this.companyName,
    this.outletName,
  });

  UserData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    emiratesIdFront = json['emirates_id_front'];
    emiratesIdBack = json['emirates_id_back'];
    visaCopy = json['visa_copy'];
    eidExpireAt = json['eid_expire_at'] == null
        ? null
        : DateTime.parse(json['eid_expire_at']);
    companyName = json['company_name'];
    outletName = json['outlet_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['emirates_id_front'] = emiratesIdFront;
    data['emirates_id_back'] = emiratesIdBack;
    data['visa_copy'] = visaCopy;
    data['eid_expire_at'] = eidExpireAt;
    data['company_name'] = companyName;
    data['outlet_name'] = outletName;
    return data;
  }
}

class UserVeririficationDataModel {
  Data? data;

  UserVeririficationDataModel({this.data});

  UserVeririficationDataModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['data'] = this.data!.toJson();
    return data;
  }
}

class Data {
  int? id;
  int? userId;
  String? emiratesIdFront;
  String? emiratesIdBack;
  String? visaCopy;
  String? eidExpireAt;
  String? companyName;
  String? outletName;
  String? createdAt;
  String? updatedAt;
  int? documentValidation;
  //Null note;

  Data({
    this.id,
    this.userId,
    this.emiratesIdFront,
    this.emiratesIdBack,
    this.visaCopy,
    this.eidExpireAt,
    this.companyName,
    this.outletName,
    this.createdAt,
    this.updatedAt,
    this.documentValidation,
    //this.note
  });

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    emiratesIdFront = json['emirates_id_front'];
    emiratesIdBack = json['emirates_id_back'];
    visaCopy = json['visa_copy'];
    eidExpireAt = json['eid_expire_at'];
    companyName = json['company_name'];
    outletName = json['outlet_name'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    documentValidation = json['document_validation'];
    //note = json['note'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['emirates_id_front'] = emiratesIdFront;
    data['emirates_id_back'] = emiratesIdBack;
    data['visa_copy'] = visaCopy;
    data['eid_expire_at'] = eidExpireAt;
    data['company_name'] = companyName;
    data['outlet_name'] = outletName;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['document_validation'] = documentValidation;
    //data['note'] = this.note;
    return data;
  }
}
