import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/shared/rest_api/api_request.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/config_model.dart';

/// Repository for splash operations
final class SplashRepository {
  /// Splash repository constructor
  const SplashRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// Get config data
  Future<ApiResult<ConfigModel>> getConfigData(
    ApiRequest request,
  ) {
    return DioRequest<ConfigModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: ConfigModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }
}
