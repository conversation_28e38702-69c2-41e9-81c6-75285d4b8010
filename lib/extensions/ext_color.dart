import 'package:flutter/material.dart';

/// Extension for color
extension ColorExt on Color {
  // amount range from 0.0 to 1.0

  /// darken color
  Color darken([double amount = .1]) {
    assert(amount >= 0 && amount <= 1, 'Amount should be between 0.0 and 1.0');

    final hsl = HSLColor.fromColor(this);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));

    return hslDark.toColor();
  }

  /// lighten color
  Color lighten([double amount = .1]) {
    assert(amount >= 0 && amount <= 1, 'Amount should be between 0.0 and 1.0');

    final hsl = HSLColor.fromColor(this);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));

    return hslLight.toColor();
  }

  /// Darken a color by [percent] amount (100 = black)
  Color darkenARGB([int percent = 10]) {
    final f = 1 - percent / 100;
    return Color.fromARGB(a.toInt(), (r * f).round(), (g * f).round(), (b * f).round());
  }

  /// Lighten a color by [percent] amount (100 = white)
  Color lightenARGB([int percent = 20]) {
    final p = percent / 100;
    return Color.fromARGB(
      a.toInt(),
      r.toInt() + ((255 - r) * p).round(),
      g.toInt() + ((255 - g) * p).round(),
      b.toInt() + ((255 - b) * p).round(),
    );
  }
}
