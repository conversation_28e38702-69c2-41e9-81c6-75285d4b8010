import 'package:flutter/material.dart';

/// Application Dark Theme
final ThemeData darkTheme = ThemeData(
  useMaterial3: false,
  fontFamily: 'Roboto',
  primaryColor: const Color(0xB3EF7822),
  secondaryHeaderColor: const Color(0xFF009f67),
  disabledColor: const Color(0xffa2a7ad),
  brightness: Brightness.dark,
  hintColor: const Color(0xFFbebebe),
  cardColor: Colors.black,
  textButtonTheme: TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: const Color(0xFFffbd5c),
    ),
  ),
  colorScheme: const ColorScheme.dark(
    primary: Color(0xFFffbd5c),
    secondary: Color(0xFFffbd5c),
    surface: Color(0xFF343636),
    error: Color(0xFFdd3135),
  ),
);
