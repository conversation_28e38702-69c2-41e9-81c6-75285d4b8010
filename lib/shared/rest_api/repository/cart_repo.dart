import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/shared/rest_api/api_request.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/cart_model.dart';

/// Repository for cart operations
final class CartRepository {
  /// Cart repository constructor
  const CartRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// Get cart data from server
  Future<ApiResult<List<CartModel>>> getCartData(
    ApiRequest request,
  ) {
    return DioRequest<List<CartModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<CartModel>((e) => CartModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Update cart on server
  Future<ApiResult<Map<String, dynamic>>> updateCart(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }
}
