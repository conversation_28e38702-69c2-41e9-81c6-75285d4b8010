import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/shared/rest_api/api_request.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/product_model.dart';

/// Repository for search operations
final class SearchRepository {
  /// Search repository constructor
  const SearchRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// Get search data
  Future<ApiResult<Map<String, dynamic>>> getSearchData(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get suggested foods
  Future<ApiResult<List<Product>>> getSuggestedFoods(
    ApiRequest request,
  ) {
    return DioRequest<List<Product>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<Product>((e) => Product.fromJson(e as Map<String, dynamic>))
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }
}
