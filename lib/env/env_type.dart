/// enum for EnvTypes
enum EnvTypes {
  /// development
  dev,

  /// Production
  prod,
}

/// ApiVersion class for setting rest, graphql version
final class ApiVersion {
  /// Returns instance of ApiVersion
  factory ApiVersion.instance() => _instance;

  ApiVersion._();

  static final ApiVersion _instance = ApiVersion._();

  static String? _restVersion;

  /// Returns rest version
  String get restVersion => _restVersion ?? 'v5';

  /// Sets rest version
  set restVersion(String version) => _restVersion = version;

  static String? _graphQlVersion;

  /// Returns graphql version
  String get graphQlVersion => _graphQlVersion ?? 'v1';

  /// Sets graphql version
  set graphQlVersion(String version) => _graphQlVersion = version;
}

/// Global variable for setting environment
late EnvTypes currentEnv;

/// Global variable for REST version
late String restVersion;

/// Global variable for GraphQl version
// late String graphQlVersion;
