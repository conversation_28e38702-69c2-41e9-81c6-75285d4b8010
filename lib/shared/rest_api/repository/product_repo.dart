import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/shared/rest_api/api_request.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/product_model.dart';

final class ProductRepository {
  /// Product repository constructor
  const ProductRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// Get popular product list
  Future<ApiResult<List<Product>>> getPopularProductList(
    ApiRequest request,
  ) {
    return DioRequest<List<Product>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<Product>((e) => Product.fromJson(e as Map<String, dynamic>))
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get reviewed product list
  Future<ApiResult<List<Product>>> getReviewedProductList(
    ApiRequest request,
  ) {
    return DioRequest<List<Product>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<Product>((e) => Product.fromJson(e as Map<String, dynamic>))
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Submit review
  Future<ApiResult<Map<String, dynamic>>> submitReview(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }

  /// Submit delivery man review
  Future<ApiResult<Map<String, dynamic>>> submitDeliveryManReview(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).post();
  }
}
