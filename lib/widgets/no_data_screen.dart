import 'package:flutter/material.dart';

import 'dart:io' show Platform;

import 'package:staffmeal_user_v2/utils/dimensions.dart';

class NoDataScreen extends StatelessWidget {
  final bool isCart;
  final String text;
  const NoDataScreen({super.key, required this.text, this.isCart = false});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: Platform.isIOS
          ? EdgeInsets.all(Dimensions.PADDING_SIZE_DEFAULT)
          : EdgeInsets.all(Dimensions.PADDING_SIZE_LARGE),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Image.asset(
            isCart ? Images.empty_cart : Images.empty_box,
            width: MediaQuery.of(context).size.height * 0.18,
            height: MediaQuery.of(context).size.height * 0.18,
          ),
          SizedBox(height: MediaQuery.of(context).size.height * 0.03),
          Text(
            isCart ? 'cart_is_empty'.tr : text,
            style: robotoMedium.copyWith(
              fontSize: MediaQuery.of(context).size.height * 0.0175,
              color: Theme.of(context).disabledColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
