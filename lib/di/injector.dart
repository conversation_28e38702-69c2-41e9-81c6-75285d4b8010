import 'package:flutter/foundation.dart' show immutable;
import 'package:get_it/get_it.dart';
import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/di/inject_blocs.dart';
import 'package:staffmeal_user_v2/di/inject_repositories.dart';
import 'package:staffmeal_user_v2/di/inject_services.dart';
import 'package:staffmeal_user_v2/env/env.dart';

/// App Dependencies Injection using GetIt
@immutable
class Injector {
  const Injector._();

  static final _injector = GetIt.instance;

  /// GetIt Instance
  static GetIt get instance => _injector;

  /// init all dependencies module wise
  static void initModules() {
    ServicesInjector(instance);

    /// use instanceName: 'open' for open api client
    ApiClientsInjector(
      injector: instance,
      baseUrl: AppEnv().baseUrl,
      isSecure: true,
      enableNativeAdapter: switch (currentEnv) {
        EnvTypes.dev => false,
        _ => true,
      },
    );
    BlocInjector(instance);
    RepositoryInjector(instance);
  }
}
