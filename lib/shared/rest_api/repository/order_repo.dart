import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:efood_multivendor/controller/auth_controller.dart';
import 'package:efood_multivendor/data/api/api_client.dart';
import 'package:efood_multivendor/data/model/body/place_order_body.dart';
import 'package:efood_multivendor/util/app_constants.dart';
import 'package:shared_preferences/shared_preferences.dart';

class OrderRepo {
  final ApiClient apiClient;
  final SharedPreferences sharedPreferences;
  OrderRepo({required this.apiClient, required this.sharedPreferences});

  Future<Response> getRunningOrderList(int offset) async {
    return await apiClient.getData(
        '${AppConstants.RUNNING_ORDER_LIST_URI}?offset=$offset&limit=10');
  }

  Future<Response> getHistoryOrderList(int offset) async {
    return await apiClient.getData(
        '${AppConstants.HISTORY_ORDER_LIST_URI}?offset=$offset&limit=10');
  }

  Future<Response> getOrderDetails(String orderID) async {
    return await apiClient.getData('${AppConstants.ORDER_DETAILS_URI}$orderID');
  }

  Future<Response> cancelOrder(String orderID) async {
    return await apiClient.postData(
        AppConstants.ORDER_CANCEL_URI, {'_method': 'put', 'order_id': orderID});
  }

  Future<Response> trackOrder(
      String orderID /*, {bool dineIn = false}*/) async {
    return await apiClient.getData(
        /*(dineIn || !Get.find<AuthController>().isLoggedIn()) ? '${AppConstants.DINE_IN_URI}?order_ids[]=$orderID'
        : */
        '${AppConstants.TRACK_URI}$orderID');
  }

  Future<Response> placeOrder(PlaceOrderBody orderBody) async {
    return await apiClient.postData(
        AppConstants.PLACE_ORDER_URI, orderBody.toJson());
  }

  Future<Response> getDeliveryManData(String orderID) async {
    return await apiClient.getData('${AppConstants.LAST_LOCATION_URI}$orderID');
  }

  Future<Response> switchToCOD(String orderID) async {
    return await apiClient.postData(
        AppConstants.COD_SWITCH_URL, {'_method': 'put', 'order_id': orderID});
  }

  Future<Response> getDistanceInMeter(
      LatLng originLatLng, LatLng destinationLatLng) async {
    return await apiClient.getData('${AppConstants.DISTANCE_MATRIX_URI}'
        '?origin_lat=${originLatLng.latitude}&origin_lng=${originLatLng.longitude}'
        '&destination_lat=${destinationLatLng.latitude}&destination_lng=${destinationLatLng.longitude}');
  }

  Future<bool> cachedOrder(String orderID) async {
    List<String> orders = [];
    orders.addAll(getCachedOrder());
    orders.add(orderID);
    return sharedPreferences.setStringList(AppConstants.DINE_IN, orders);
  }

  List<String> getCachedOrder() {
    List<String> orders = [];
    if (sharedPreferences.containsKey(AppConstants.DINE_IN)) {
      orders
          .addAll(sharedPreferences.getStringList(AppConstants.DINE_IN) ?? []);
    }
    return orders;
  }

  Future<Response> getDineInOrders() async {
    List<String> dineInOrders = getCachedOrder();
    String orderIds = '';
    for (String id in dineInOrders) {
      orderIds += '${orderIds.isEmpty ? '?' : '&'}order_ids[]=$id';
    }
    return await apiClient.getData(
        '${AppConstants.DINE_IN_URI}${Get.find<AuthController>().isLoggedIn() ? '' : orderIds}');
  }

  Future<Response> callToCCAvenueServer(
      String orderID, Map<String, dynamic> result) async {
    return await apiClient.postData(
      AppConstants.CC_AVENUE_CAPTURE,
      {
        'order_id': orderID,
        'card_type': 'CRDC',
        'network': result['paymentMethod']['network'],
        'payment_data': result['token']
      },
    );
  }
}
