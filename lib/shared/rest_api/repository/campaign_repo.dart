import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/shared/rest_api/api_request.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/basic_campaign_model.dart';

/// Repository for campaign operations
final class CampaignRepository {
  /// Campaign repository constructor
  const CampaignRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// Get basic campaign list
  Future<ApiResult<List<BasicCampaignModel>>> getBasicCampaignList(
    ApiRequest request,
  ) {
    return DioRequest<List<BasicCampaignModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<BasicCampaignModel>(
            (e) => BasicCampaignModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get campaign details
  Future<ApiResult<BasicCampaignModel>> getCampaignDetails(
    ApiRequest request,
  ) {
    return DioRequest<BasicCampaignModel>(
      dio: dio,
      path: request.path!,
      jsonMapper: BasicCampaignModel.fromJson,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get item campaign list
  Future<ApiResult<List<BasicCampaignModel>>> getItemCampaignList(
    ApiRequest request,
  ) {
    return DioRequest<List<BasicCampaignModel>>(
      dio: dio,
      path: request.path!,
      listJsonMapper: (json) => json
          .map<BasicCampaignModel>(
            (e) => BasicCampaignModel.fromJson(e as Map<String, dynamic>),
          )
          .toList(),
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }
}
