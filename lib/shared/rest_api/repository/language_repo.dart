import 'package:pkg_dio/pkg_dio.dart';
import 'package:staffmeal_user_v2/shared/rest_api/api_request.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/language_model.dart';

/// Repository for language operations
final class LanguageRepository {
  /// Language repository constructor
  const LanguageRepository({required this.dio});

  /// define dio variable
  final Dio dio;

  /// Get all languages from server
  Future<ApiResult<Map<String, dynamic>>> getAllLanguages(
    ApiRequest request,
  ) {
    return DioRequest<Map<String, dynamic>>(
      dio: dio,
      path: request.path!,
      jsonMapper: (json) => json,
      cancelToken: request.cancelToken,
      hideKeyboard: request.hideKeyboard,
      data: request.data,
      options: request.options,
      params: request.params,
      receiveProgress: request.receiveProgress,
    ).get();
  }

  /// Get supported languages locally
  List<LanguageModel> getSupportedLanguages() {
    return [
      LanguageModel(
        imageUrl: '',
        languageName: 'English',
        countryCode: 'US',
        languageCode: 'en',
      ),
      LanguageModel(
        imageUrl: '',
        languageName: 'Arabic',
        countryCode: 'SA',
        languageCode: 'ar',
      ),
    ];
  }
}
