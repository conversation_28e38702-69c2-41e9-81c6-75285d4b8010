import 'package:flutter/material.dart';
import 'package:staffmeal_user_v2/utils/dimensions.dart';

class QuantityButton extends StatelessWidget {
  final bool isIncrement;
  final Function() onTap;
  const QuantityButton({
    super.key,
    required this.isIncrement,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 22,
        width: 22,
        margin: const EdgeInsets.symmetric(
          horizontal: Dimensions.PADDING_SIZE_SMALL,
        ),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            width: 1,
            color: isIncrement
                ? Theme.of(context).primaryColor
                : Theme.of(context).disabledColor,
          ),
          color: isIncrement
              ? Theme.of(context).primaryColor
              : Theme.of(context).disabledColor.withOpacity(0.2),
        ),
        alignment: Alignment.center,
        child: Icon(
          isIncrement ? Icons.add : Icons.remove,
          size: 15,
          color: isIncrement
              ? Theme.of(context).cardColor
              : Theme.of(context).disabledColor,
        ),
      ),
    );
  }
}
