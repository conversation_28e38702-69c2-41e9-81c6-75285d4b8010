class RestaurantBody {
  String? restaurantName;
  String? restaurantAddress;
  //String vat;
  String? minDeliveryTime;
  String? maxDeliveryTime;
  String? lat;
  String? lng;
  String? fName;
  String? lName;
  String? phone;
  String? email;
  String? password;
  String? zoneId;

  RestaurantBody({
    this.restaurantName,
    this.restaurantAddress,
    //this.vat,
    this.minDeliveryTime,
    this.maxDeliveryTime,
    this.lat,
    this.lng,
    this.fName,
    this.lName,
    this.phone,
    this.email,
    this.password,
    this.zoneId,
  });

  RestaurantBody.fromJson(Map<String, dynamic> json) {
    restaurantName = json['restaurant_name']?.toString();
    restaurantAddress = json['restaurant_address']?.toString();
    // vat = json['vat'];
    minDeliveryTime = json['min_delivery_time']?.toString();
    maxDeliveryTime = json['max_delivery_time']?.toString();
    lat = json['lat']?.toString();
    lng = json['lng']?.toString();
    fName = json['fName']?.toString();
    lName = json['lName']?.toString();
    phone = json['phone']?.toString();
    email = json['email']?.toString();
    password = json['password']?.toString();
    zoneId = json['zone_id']?.toString();
  }

  Map<String, String> toJson() {
    final Map<String, String> data = <String, String>{};
    data['restaurant_name'] = restaurantName ?? '';
    data['restaurant_address'] = restaurantAddress ?? '';
    // data['vat'] = this.vat;
    data['min_delivery_time'] = minDeliveryTime ?? '';
    data['max_delivery_time'] = maxDeliveryTime ?? '';
    data['lat'] = lat ?? '';
    data['lng'] = lng ?? '';
    data['fName'] = fName ?? '';
    data['lName'] = lName ?? '';
    data['phone'] = phone ?? '';
    data['email'] = email ?? '';
    data['password'] = password ?? '';
    data['zone_id'] = zoneId ?? '';
    return data;
  }
}
