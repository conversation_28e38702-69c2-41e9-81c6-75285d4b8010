class TimeSlotModel {
  int? day;
  DateTime? startTime;
  DateTime? endTime;

  TimeSlotModel(
      {required this.day, required this.startTime, required this.endTime});

  TimeSlotModel.fromJson(Map<String, dynamic> json) {
    day = json['day']  == null ? 0 : int.parse(json['day'].toString());
    startTime = json['start_time'] ==null?null: DateTime.parse(json['start_time'].toString());
    endTime = json['end_time'] ==null?null: DateTime.parse(json['end_time'].toString());
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['day'] = day;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    return data;
  }
}
