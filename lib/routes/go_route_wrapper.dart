import 'package:flutter/widgets.dart';

/// GoRouteWrapper for GoRouter builder
class GoRouteWrapper extends StatefulWidget {
  /// Default constructor
  const GoRouteWrapper({
    required this.child,
    this.onInit,
    this.onChangeDependencies,
    this.onAfterLayout,
    this.onDispose,
    this.isRegistered = false,
    super.key,
  });

  /// The child widget
  final Widget child;

  /// The onInit function callback function
  final void Function()? onInit;

  /// The onChangeDependencies function callback function
  final void Function(BuildContext context)? onChangeDependencies;

  /// The onAfterLayout function callback function
  final void Function(BuildContext context)? onAfterLayout;

  /// The onDispose function callback function
  final void Function(BuildContext context)? onDispose;

  /// isRegistered to check need to call dispose
  final bool isRegistered;

  @override
  State<GoRouteWrapper> createState() => _GoRouteWrapperState();
}

class _GoRouteWrapperState extends State<GoRouteWrapper> with AutomaticKeepAliveClientMixin {
  late final bool isRegistered;
  @override
  void initState() {
    isRegistered = widget.isRegistered;
    super.initState();
    widget.onInit?.call();
    if (widget.onAfterLayout == null) return;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onAfterLayout?.call(context);
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    widget.onChangeDependencies?.call(context);
  }

  @override
  void dispose() {
    if (!isRegistered) widget.onDispose?.call(context);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => true;
}
