import 'dart:convert';

import 'package:staffmeal_user_v2/shared/rest_api/model/response/conversation_model.dart';

class MessageModel {
  int? totalSize;
  int? limit;
  int? offset;
  bool? status;
  Conversation? conversation;
  List<Message>? messages;

  MessageModel({
    this.totalSize,
    this.limit,
    this.offset,
    this.status,
    this.conversation,
    this.messages,
  });

  MessageModel.fromJson(Map<String, dynamic> json) {
    totalSize = json['total_size'] != null
        ? int.tryParse(json['total_size'].toString())
        : null;
    limit = json['limit'] != null
        ? int.tryParse(json['limit'].toString())
        : null;
    offset = json['offset'] != null
        ? int.tryParse(json['offset'].toString())
        : null;
    status = json['status'] != null ? json['status'] as bool : null;
    conversation = json['conversation'] != null
        ? Conversation.fromJson(json['conversation'] as Map<String, dynamic>)
        : null;
    if (json['messages'] != null) {
      messages = <Message>[];
      json['messages'].forEach((dynamic v) {
        messages!.add(Message.fromJson(v as Map<String, dynamic>));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['total_size'] = totalSize;
    data['limit'] = limit;
    data['offset'] = offset;
    data['status'] = status;
    if (conversation != null) {
      data['conversation'] = conversation!.toJson();
    }
    if (messages != null) {
      data['messages'] = messages!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Message {
  int? id;
  int? conversationId;
  int? senderId;
  String? message;
  List<String>? files;
  int? isSeen;
  String? createdAt;
  String? updatedAt;

  Message({
    this.id,
    this.conversationId,
    this.senderId,
    this.message,
    this.files,
    this.isSeen,
    this.createdAt,
    this.updatedAt,
  });

  Message.fromJson(Map<String, dynamic> json) {
    id = json['id'] != null ? int.tryParse(json['id'].toString()) : null;
    conversationId = json['conversation_id'] != null
        ? int.tryParse(json['conversation_id'].toString())
        : null;
    senderId = json['sender_id'] != null
        ? int.tryParse(json['sender_id'].toString())
        : null;
    message = json['message']?.toString();
    files = (json['file'] != 'null' && json['file'] != null)
        ? List<String>.from(jsonDecode(json['file'].toString()) as List)
        : [];
    isSeen = json['is_seen'] != null
        ? int.tryParse(json['is_seen'].toString())
        : null;
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['conversation_id'] = conversationId;
    data['sender_id'] = senderId;
    data['message'] = message;
    data['file'] = files;
    data['is_seen'] = isSeen;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}
