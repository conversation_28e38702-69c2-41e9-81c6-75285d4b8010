class WalletModel {
  int? totalSize;
  String? limit;
  String? offset;
  List<Transaction>? data;

  WalletModel({this.totalSize, this.limit, this.offset, this.data});

  WalletModel.fromJson(Map<String, dynamic> json) {
    totalSize = json["total_size"] == null
        ? 0
        : int.parse(json['total_size'].toString());
    limit = json["limit"] == null ? '' : json['limit'].toString();
    offset = json["offset"] == null ? '' : json['offset'].toString();
    if (json['data'] != null) {
      data = [];
      json['data'].forEach((v) {
        data?.add(Transaction.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['total_size'] = totalSize;
    data['limit'] = limit;
    data['offset'] = offset;
    if (this.data != null) {
      data['data'] = this.data?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Transaction {
  int? userId;
  String? transactionId;
  double? credit;
  double? debit;
  double? adminBonus;
  double? balance;
  String? transactionType;
  DateTime? createdAt;
  DateTime? updatedAt;

  Transaction({
    this.userId,
    this.transactionId,
    this.credit,
    this.debit,
    this.adminBonus,
    this.balance,
    this.transactionType,
    this.createdAt,
    this.updatedAt,
  });

  Transaction.fromJson(Map<String, dynamic> json) {
    userId =
        json["user_id"] == null ? 0 : int.parse(json['user_id'].toString());
    transactionId =
        json["transaction_id"] == null ? '' : json['transaction_id'].toString();
    credit =
        json["credit"] == null ? 0 : double.parse(json['credit'].toString());
    debit = json["debit"] == null ? 0 : double.parse(json['debit'].toString());
    if (json["admin_bonus"] != null) {
      adminBonus = json["admin_bonus"] == null
          ? 0
          : double.parse(json['admin_bonus'].toString());
    }
    balance =
        json["balance"] == null ? 0 : double.parse(json['balance'].toString());
    transactionType = json["transaction_type"] == null
        ? ''
        : json['transaction_type'].toString();
    createdAt = json["created_at"] == null
        ? null
        : DateTime.parse(json['created_at'].toString());
    updatedAt = json["updated_at"] == null
        ? null
        : DateTime.parse(json['updated_at'].toString());
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data["user_id"] = userId;
    data["transaction_id"] = transactionId;
    data["credit"] = credit;
    data["debit"] = debit;
    data["admin_bonus"] = adminBonus;
    data["balance"] = balance;
    data["transaction_type"] = transactionType;
    data["created_at"] = createdAt?.toIso8601String();
    data["updated_at"] = updatedAt?.toIso8601String();
    return data;
  }
}
