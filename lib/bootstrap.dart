import 'dart:async';

import 'package:firebase_core/firebase_core.dart';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staffmeal_user_v2/di/injector.dart';
import 'package:staffmeal_user_v2/utils/app_bloc_observer.dart';
import 'package:staffmeal_user_v2/utils/logger.dart';

/// first function to be called from main function
Future<void> bootstrap(
  FutureOr<Widget> Function() builder,
) async {
  Bloc.observer = const AppBlocObserver();
  FlutterError.onError = (details) {
    details.exceptionAsString().logE;
    details.stack.logE;
  };
  // Disable logging for release mode
  if (kReleaseMode) {
    debugPrint = (String? message, {int? wrapWidth}) {};
  }
  unawaited(
    runZonedGuarded(
      () async {
        WidgetsFlutterBinding.ensureInitialized();
        await Firebase.initializeApp();

        await SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
        ]); // lock orientation
        await _initialization();
        // if (currentEnv == EnvTypes.production && kReleaseMode) {
        //   await Injector.instance<CrashlyticsManager>().embrace.start(() async => runApp(await builder()));
        // } else {
        runApp(await builder());
        // }
      },
      (error, stack) {
        error.logE;
        stack.logE;
        // Injector.instance<CrashlyticsManager>().logHandledDartError(error: error, stackTrace: stack);
        // if (kReleaseMode && currentEnv == EnvTypes.production) FirebaseCrashlytics.instance.recordError(error, stack);
      },
    ),
  );
}

Future<void> _initialization() async {
  Injector.initModules();
  await Injector.instance.allReady();
}
