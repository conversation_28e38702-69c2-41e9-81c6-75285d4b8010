import 'package:flutter/foundation.dart' show immutable;

/// This class contains all keys name used to store values locally
@immutable
final class ApiKeys {
  const ApiKeys._();

  /// user email
  static const email = 'email';

  /// user phone number
  static const phoneNumber = 'phone_number';

  /// user country code
  static const countryCode = 'country_code';

  /// user's mobile token
  static const deviceId = 'device_id';

  /// device token
  static const deviceToken = 'device_token';

  /// 4-digit verification code
  static const otp = 'otp';

  /// user password
  static const password = 'password';

  /// key for fcm token
  static const fcmToken = 'fcm_token';
}
