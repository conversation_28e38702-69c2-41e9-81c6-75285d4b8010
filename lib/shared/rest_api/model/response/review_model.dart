class ReviewModel {
  int? id;
  String? comment;
  int? rating;
  String? foodName;
  String? foodImage;
  String? customerName;
  String? createdAt;
  String? updatedAt;

  ReviewModel(
      {this.id,
      this.comment,
      this.rating,
      this.foodName,
      this.foodImage,
      this.customerName,
      this.createdAt,
      this.updatedAt});

  ReviewModel.fromJson(Map<String, dynamic> json) {
    id = json['id'] == null ? 0 : int.parse(json['id'].toString());
    comment = json['comment'] == null ? '' : json['comment'].toString();
    rating = json['rating'] == null ? 0 : int.parse(json['rating'].toString());
    foodName = json['food_name'] == null ? '' : json['food_name'].toString();
    foodImage = json['food_image'] == null ? '' : json['food_image'].toString();
    customerName =
        json['customer_name'] == null ? '' : json['customer_name'].toString();
    createdAt = json['created_at'] == null ? '' : json['created_at'].toString();
    updatedAt = json['updated_at'] == null ? '' : json['updated_at'].toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['comment'] = comment;
    data['rating'] = rating;
    data['food_name'] = foodName;
    data['food_image'] = foodImage;
    data['customer_name'] = customerName;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}
