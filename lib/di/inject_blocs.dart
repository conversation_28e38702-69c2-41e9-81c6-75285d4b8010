import 'package:flutter/foundation.dart' show immutable;
import 'package:get_it/get_it.dart';


/// Cubits/Blocs injection
@immutable
final class BlocInjector {
  /// Construct
  BlocInjector(this.instance) {
    _init();
  }

  /// GetIt instance
  final GetIt instance;

  void _init() {
    // Injector.instance.registerSingleton(AppCubit());
    // Injector.instance.registerLazySingleton(() => AppLanguageCubit(settingsRepository: Injector.instance()));
  }
}
