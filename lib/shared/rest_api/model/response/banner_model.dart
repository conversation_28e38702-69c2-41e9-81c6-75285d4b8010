import 'package:staffmeal_user_v2/shared/rest_api/model/response/basic_campaign_model.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/product_model.dart';
import 'package:staffmeal_user_v2/shared/rest_api/model/response/restaurant_model.dart';

class BannerModel {
  List<BasicCampaignModel>? campaigns;
  List<Banner>? banners;

  BannerModel({this.campaigns, this.banners});

  BannerModel.fromJson(Map<String, dynamic> json) {
    if (json['campaigns'] != null) {
      campaigns = [];
      json['campaigns'].forEach((dynamic v) {
        campaigns?.add(BasicCampaignModel.fromJson(v as Map<String, dynamic>));
      });
    }
    if (json['banners'] != null) {
      banners = [];
      json['banners'].forEach((dynamic v) {
        banners?.add(Banner.fromJson(v as Map<String, dynamic>));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (campaigns != null) {
      data['campaigns'] = campaigns?.map((v) => v.toJson()).toList();
    }
    if (banners != null) {
      data['banners'] = banners?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Banner {
  int? id;
  String? title;
  String? type;
  String? image;
  Restaurant? restaurant;
  Product? food;

  Banner({
    this.id,
    this.title,
    this.type,
    this.image,
    this.restaurant,
    this.food,
  });

  Banner.fromJson(Map<String, dynamic> json) {
    id = json['id'] == null ? 0 : int.parse(json['id'].toString());
    title = json['title'] == null ? '' : json['title'].toString();
    type = json['type'] == null ? '' : json['type'].toString();
    image = json['image'] == null ? '' : json['image'].toString();
    restaurant = json['restaurant'] != null
        ? Restaurant.fromJson(json['restaurant'] as Map<String, dynamic>)
        : null;
    food = json['food'] != null
        ? Product.fromJson(json['food'] as Map<String, dynamic>)
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['type'] = type;
    data['image'] = image;
    if (restaurant != null) {
      data['restaurant'] = restaurant?.toJson();
    }
    if (food != null) {
      data['food'] = food?.toJson();
    }
    return data;
  }
}
