class RestaurantModel {
  int? totalSize;
  String? limit;
  int? offset;
  List<Restaurant>? restaurants;

  RestaurantModel({this.totalSize, this.limit, this.offset, this.restaurants});

  RestaurantModel.fromJson(Map<String, dynamic> json) {
    totalSize = json['total_size'];
    limit = json['limit'].toString();
    offset =
        (json['offset'] != null && json['offset'].toString().trim().isNotEmpty)
        ? int.parse(json['offset'].toString())
        : null;
    if (json['restaurants'] != null) {
      restaurants = [];
      json['restaurants'].forEach((dynamic v) {
        restaurants?.add(Restaurant.fromJson(v as Map<String, dynamic>));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['total_size'] = totalSize;
    data['limit'] = limit;
    data['offset'] = offset;
    if (restaurants != null) {
      data['restaurants'] = restaurants?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Restaurant {
  int? id;
  String? name;
  String? phone;
  String? email;
  String? logo;
  String? latitude;
  String? longitude;
  String? address;
  int? zoneId;
  double? minimumOrder;
  String? currency;
  bool? freeDelivery;
  String? coverPhoto;
  bool? delivery;
  bool? takeAway;
  bool? dineIn;
  bool? scheduleOrder;
  double? avgRating;
  double? tax;
  int? ratingCount;
  int? selfDeliverySystem;
  bool? posSystem;
  int? open;
  bool? active;
  String? deliveryTime;
  List<int>? categoryIds;
  int? veg;
  int? nonVeg;
  Discount? discount;
  List<Schedules>? schedules;
  List<Schedules>? deliverySchedule;
  double? minimumShippingCharge;
  double? perKmShippingCharge;
  int? vendorId;
  List<Tables>? tables;
  double? convenienceFee;
  String? convenienceType;

  Restaurant({
    this.id,
    this.name,
    this.phone,
    this.email,
    this.logo,
    this.latitude,
    this.longitude,
    this.address,
    this.zoneId,
    this.minimumOrder,
    this.currency,
    this.freeDelivery,
    this.coverPhoto,
    this.delivery,
    this.takeAway,
    this.dineIn,
    this.scheduleOrder,
    this.avgRating,
    this.tax,
    this.ratingCount,
    this.selfDeliverySystem,
    this.posSystem,
    this.open,
    this.active,
    this.deliveryTime,
    this.categoryIds,
    this.veg,
    this.nonVeg,
    this.discount,
    this.schedules,
    this.minimumShippingCharge,
    this.perKmShippingCharge,
    this.vendorId,
    this.tables,
    this.convenienceFee,
    this.convenienceType,
  });

  Restaurant.fromJson(Map<String, dynamic> json) {
    id = json['id'] == null ? 0 : int.parse(json['id'].toString());
    name = json['name'] == null ? '' : json['name'].toString();
    phone = json['phone'] == null ? '' : json['phone'].toString();
    email = json['email'] == null ? '' : json['email'].toString();
    logo = json['logo'] == null ? '' : json['logo'].toString();
    latitude = json['latitude'] == null ? '' : json['latitude'].toString();
    longitude = json['longitude'] == null ? '' : json['longitude'].toString();
    address = json['address'] == null ? '' : json['address'].toString();
    zoneId = json['zone_id'] == null
        ? 0
        : int.parse(json['zone_id'].toString());
    minimumOrder = json['minimum_order'] == null
        ? 0
        : double.parse(json['minimum_order'].toString());
    currency = json['currency'] == null ? '' : json['currency'].toString();
    freeDelivery = json['free_delivery'] == null
        ? false
        : bool.parse(json['free_delivery'].toString());
    coverPhoto = json['cover_photo'] == null
        ? ''
        : json['cover_photo'].toString();
    delivery = json['delivery'] == null
        ? false
        : bool.parse(json['delivery'].toString());
    takeAway = json['take_away'] == null
        ? false
        : bool.parse(json['take_away'].toString());
    dineIn = json['dine_in'] == 1;
    scheduleOrder = json['schedule_order'] == null
        ? false
        : bool.parse(json['schedule_order'].toString());
    avgRating = json['avg_rating'] == null
        ? 0
        : double.parse(json['avg_rating'].toString());
    tax = json['tax'] == null ? 0 : double.parse(json['tax'].toString());
    ratingCount = json['rating_count'] == null
        ? 0
        : int.parse(json['rating_count'].toString());
    selfDeliverySystem = json['self_delivery_system'] == null
        ? 0
        : int.parse(json['self_delivery_system'].toString());
    posSystem = json['pos_system'] == null
        ? false
        : bool.parse(json['pos_system'].toString());
    open = json['open'] == null ? 0 : int.parse(json['open'].toString());
    active = json['active'] == null
        ? false
        : bool.parse(json['active'].toString());
    deliveryTime = json['delivery_time'] == null
        ? ''
        : json['delivery_time'].toString();
    veg = json['veg'] == null ? 0 : int.parse(json['veg'].toString());
    nonVeg = json['non_veg'] == null
        ? 0
        : int.parse(json['non_veg'].toString());
    categoryIds = json['category_ids'] != null
        ? List<int>.from(json['category_ids'] as List)
        : [];
    discount = json['discount'] != null
        ? Discount.fromJson(json['discount'] as Map<String, dynamic>)
        : null;
    if (json['schedules'] != null) {
      schedules = <Schedules>[];
      json['schedules'].forEach((dynamic v) {
        schedules?.add(Schedules.fromJson(v as Map<String, dynamic>));
      });
    }
    deliverySchedule = json['delivery_schedules'] == null
        ? []
        : List<Schedules>.from(
            (json['delivery_schedules'] as List<dynamic>?)?.map(
                  (x) => Schedules.fromJson(x as Map<String, dynamic>),
                ) ??
                [],
          );
    minimumShippingCharge = json['minimum_shipping_charge'] != null
        ? double.tryParse(json['minimum_shipping_charge'].toString()) ?? 0.0
        : 0.0;
    perKmShippingCharge = json['per_km_shipping_charge'] != null
        ? double.tryParse(json['per_km_shipping_charge'].toString()) ?? 0.0
        : 0.0;
    vendorId = json['vendor_id'];
    tables = <Tables>[];
    if (json['tables'] != null) {
      json['tables'].forEach((dynamic v) {
        tables?.add(Tables.fromJson(v as Map<String, dynamic>));
      });
    }
    convenienceFee = json['convenience_fee'] == null
        ? 0
        : double.parse(json['convenience_fee'].toString());
    convenienceType = json['convenience_fee_type'] == 'percentage'
        ? 'percent'
        : 'amount';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['phone'] = phone;
    data['email'] = email;
    data['logo'] = logo;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['address'] = address;
    data['minimum_order'] = minimumOrder;
    data['currency'] = currency;
    data['zone_id'] = zoneId;
    data['free_delivery'] = freeDelivery;
    data['cover_photo'] = coverPhoto;
    data['delivery'] = delivery;
    data['take_away'] = takeAway;
    data['dine_in'] = dineIn;
    data['schedule_order'] = scheduleOrder;
    data['avg_rating'] = avgRating;
    data['tax'] = tax;
    data['rating_count '] = ratingCount;
    data['self_delivery_system'] = selfDeliverySystem;
    data['pos_system'] = posSystem;
    data['open'] = open;
    data['active'] = active;
    data['veg'] = veg;
    data['non_veg'] = nonVeg;
    data['delivery_time'] = deliveryTime;
    data['category_ids'] = categoryIds;
    if (discount != null) {
      data['discount'] = discount?.toJson();
    }
    if (schedules != null) {
      data['schedules'] = schedules?.map((v) => v.toJson()).toList();
    }
    if (deliverySchedule != null) {
      data['delivery_schedule'] = deliverySchedule
          ?.map((v) => v.toJson())
          .toList();
    }
    data['minimum_shipping_charge'] = minimumShippingCharge;
    data['per_km_shipping_charge'] = perKmShippingCharge;
    data['vendor_id'] = vendorId;
    if (tables != null) {
      data['tables'] = tables?.map((v) => v.toJson()).toList();
    }
    data['convenience_fee'] = convenienceFee;
    data['convenience_fee_type'] = convenienceType;
    return data;
  }
}

class Discount {
  int? id;
  String? startDate;
  String? endDate;
  String? startTime;
  String? endTime;
  double? minPurchase;
  double? maxDiscount;
  double? discount;
  String? discountType;
  int? restaurantId;
  String? createdAt;
  String? updatedAt;

  Discount({
    this.id,
    this.startDate,
    this.endDate,
    this.startTime,
    this.endTime,
    this.minPurchase,
    this.maxDiscount,
    this.discount,
    this.discountType,
    this.restaurantId,
    this.createdAt,
    this.updatedAt,
  });

  Discount.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    startTime = json['start_time']?.substring(0, 5) ?? "00:00";
    endTime = json['end_time']?.substring(0, 5) ?? "00:00";
    minPurchase = json['min_purchase'].toDouble();
    maxDiscount = json['max_discount'].toDouble();
    discount = json['discount'].toDouble();
    discountType = json['discount_type'];
    restaurantId = json['restaurant_id'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    data['min_purchase'] = minPurchase;
    data['max_discount'] = maxDiscount;
    data['discount'] = discount;
    data['discount_type'] = discountType;
    data['restaurant_id'] = restaurantId;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}

class Schedules {
  int? id;
  int? restaurantId;
  int? day;
  String? openingTime;
  String? closingTime;

  Schedules({
    this.id,
    this.restaurantId,
    this.day,
    this.openingTime,
    this.closingTime,
  });

  Schedules.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    restaurantId = json['restaurant_id'];
    day = json['day'];
    openingTime = json['opening_time'].substring(0, 5);
    closingTime = json['closing_time'].substring(0, 5);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['restaurant_id'] = restaurantId;
    data['day'] = day;
    data['opening_time'] = openingTime;
    data['closing_time'] = closingTime;
    return data;
  }
}

class Tables {
  int? id;
  String? title;
  String? no;
  int? capacity;

  Tables({this.id, this.title, this.no, this.capacity});

  Tables.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    no = json['no'];
    capacity = json['capacity'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['no'] = no;
    data['capacity'] = capacity;
    return data;
  }
}
