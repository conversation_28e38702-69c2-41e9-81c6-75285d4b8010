// 
// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';

/// Theme colors
@immutable
class ThemeColors extends ThemeExtension<ThemeColors> {
  /// constructor
  const ThemeColors({
    required this.primary,
    required this.scaffold,
    required this.appBar,
  });

  final Color primary;
  final Color scaffold;
  final Color appBar;

  @override
  ThemeExtension<ThemeColors> copyWith({
    Color? primary,
    Color? scaffold,
    Color? appBar,
  }) {
    return ThemeColors(
      primary: primary ?? this.primary,
      scaffold: scaffold ?? this.scaffold,
      appBar: appBar ?? this.appBar,
    );
  }

  @override
  ThemeExtension<ThemeColors> lerp(covariant ThemeExtension<ThemeColors>? other, double t) {
    if (other is! ThemeColors) {
      return this;
    }
    return ThemeColors(
      primary: Color.lerp(primary, other.primary, t)!,
      scaffold: Color.lerp(scaffold, other.scaffold, t)!,
      appBar: Color.lerp(appBar, other.appBar, t)!,
    );
  }
}
